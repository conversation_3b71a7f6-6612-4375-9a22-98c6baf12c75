/**
 * 字体管理器
 * 负责动态加载fonts文件夹中的字体文件
 */
class FontManager {
    constructor() {
        this.fonts = [];
        this.loadedFonts = new Set();
        this.defaultFont = '沐瑶软笔手写体';
    }
    
    /**
     * 初始化字体管理器
     */
    async init() {
        try {
            await this.loadFontsFromDirectory();
            this.updateFontSelector();
            console.log('字体管理器初始化完成，加载字体:', this.fonts);
        } catch (error) {
            console.error('字体管理器初始化失败:', error);
            // 使用默认字体
            this.fonts = [{ name: this.defaultFont, file: '沐瑶软笔手写体.ttf' }];
            this.updateFontSelector();
        }
    }
    
    /**
     * 从fonts目录加载字体文件
     */
    async loadFontsFromDirectory() {
        try {
            // 使用Electron API读取fonts目录
            if (window.electronAPI && window.electronAPI.readFontsDirectory) {
                const fontFiles = await window.electronAPI.readFontsDirectory();
                console.log('发现字体文件:', fontFiles);
                
                this.fonts = [];
                
                for (const file of fontFiles) {
                    if (this.isFontFile(file)) {
                        const fontName = this.extractFontName(file);
                        this.fonts.push({
                            name: fontName,
                            file: file,
                            path: `fonts/${file}`
                        });

                        // 动态加载字体
                        await this.loadFont(fontName, `fonts/${file}`);
                    }
                }
                
                // 如果没有找到字体，使用默认字体
                if (this.fonts.length === 0) {
                    this.fonts.push({
                        name: this.defaultFont,
                        file: '沐瑶软笔手写体.ttf',
                        path: 'fonts/沐瑶软笔手写体.ttf'
                    });
                    await this.loadFont(this.defaultFont, 'fonts/沐瑶软笔手写体.ttf');
                }
                
            } else {
                // 如果electronAPI不可用，使用预定义的字体列表
                console.warn('electronAPI不可用，使用预定义字体列表');
                await this.loadPredefinedFonts();
            }
        } catch (error) {
            console.error('加载字体目录失败:', error);
            await this.loadPredefinedFonts();
        }
    }
    
    /**
     * 加载预定义的字体列表
     */
    async loadPredefinedFonts() {
        const predefinedFonts = [
            { name: '沐瑶软笔手写体', file: '沐瑶软笔手写体.ttf' },
            { name: '包图小白体', file: '包图小白体.ttf' },
            { name: '思源柔黑', file: '思源柔黑.ttf' },
            { name: '清松手寫體1', file: '清松手寫體1.ttf' }
        ];
        
        this.fonts = [];
        
        for (const font of predefinedFonts) {
            this.fonts.push({
                name: font.name,
                file: font.file,
                path: `fonts/${font.file}`
            });

            try {
                await this.loadFont(font.name, `fonts/${font.file}`);
            } catch (error) {
                console.warn(`加载字体 ${font.name} 失败:`, error);
            }
        }
    }
    
    /**
     * 动态加载单个字体
     */
    async loadFont(fontName, fontPath) {
        if (this.loadedFonts.has(fontName)) {
            return; // 已经加载过了
        }
        
        try {
            // 创建@font-face规则
            const fontFace = new FontFace(fontName, `url('${fontPath}')`);
            await fontFace.load();
            
            // 添加到document.fonts
            document.fonts.add(fontFace);
            this.loadedFonts.add(fontName);
            
            console.log(`字体 ${fontName} 加载成功`);
        } catch (error) {
            console.error(`加载字体 ${fontName} 失败:`, error);
        }
    }
    
    /**
     * 判断是否为字体文件
     */
    isFontFile(filename) {
        const fontExtensions = ['.ttf', '.otf', '.woff', '.woff2'];
        return fontExtensions.some(ext => filename.toLowerCase().endsWith(ext));
    }
    
    /**
     * 从文件名提取字体名称
     */
    extractFontName(filename) {
        // 移除文件扩展名
        return filename.replace(/\.(ttf|otf|woff|woff2)$/i, '');
    }
    
    /**
     * 更新字体选择器
     */
    updateFontSelector() {
        const fontSelector = document.getElementById('font-family');
        if (!fontSelector) return;
        
        // 清空现有选项
        fontSelector.innerHTML = '';
        
        // 添加字体选项
        this.fonts.forEach(font => {
            const option = document.createElement('option');
            option.value = font.name;
            option.textContent = font.name;
            option.style.fontFamily = font.name;
            fontSelector.appendChild(option);
        });
        
        // 设置默认选择
        if (this.fonts.length > 0) {
            fontSelector.value = this.defaultFont;
        }
    }
    
    /**
     * 获取所有可用字体
     */
    getAvailableFonts() {
        return this.fonts;
    }
    
    /**
     * 检查字体是否已加载
     */
    isFontLoaded(fontName) {
        return this.loadedFonts.has(fontName);
    }
    
    /**
     * 获取默认字体
     */
    getDefaultFont() {
        return this.defaultFont;
    }
}

// 导出字体管理器
window.FontManager = FontManager;
