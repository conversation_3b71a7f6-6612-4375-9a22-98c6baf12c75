<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>漫画对话气泡编辑器</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 顶部工具栏 -->
        <header class="toolbar">
            <div class="toolbar-section">
                <button id="import-btn" class="btn btn-primary">
                    <span class="btn-icon">📁</span>
                    导入图片
                </button>
            </div>
            
            <div class="toolbar-section flex-grow">
                <div class="input-group">
                    <input type="text" id="text-input" placeholder="在这里输入对话内容..." class="text-input">
                    <button id="add-bubble-btn" class="btn btn-secondary">
                        <span class="btn-icon">💬</span>
                        添加气泡
                    </button>
                </div>
            </div>
            
            <div class="toolbar-section">
                <button id="export-btn" class="btn btn-success" disabled>
                    <span class="btn-icon">💾</span>
                    导出图片
                </button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 画布容器 -->
            <div id="canvas-container" class="canvas-container">
                <div class="canvas-wrapper">
                    <canvas id="main-canvas"></canvas>
                </div>
                
                <!-- 空状态提示 -->
                <div id="empty-state" class="empty-state">
                    <div class="empty-state-content">
                        <div class="empty-state-icon">🖼️</div>
                        <h3>开始创建你的漫画对话</h3>
                        <p>点击"导入图片"按钮或直接拖拽图片到此处</p>
                        <button class="btn btn-primary" onclick="document.getElementById('import-btn').click()">
                            选择图片
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 侧边工具面板 -->
            <aside class="side-panel">
                <div class="panel-section">
                    <h4>气泡样式</h4>
                    <div class="style-controls">
                        <div class="control-group">
                            <label>字体大小</label>
                            <input type="range" id="font-size-slider" min="12" max="48" value="16" class="slider">
                            <span id="font-size-value">16px</span>
                        </div>
                        
                        <div class="control-group">
                            <label>气泡颜色</label>
                            <div class="color-options">
                                <button class="color-btn active" data-color="#ffffff" style="background: #ffffff; border: 2px solid #ccc;"></button>
                                <button class="color-btn" data-color="#f0f0f0" style="background: #f0f0f0;"></button>
                                <button class="color-btn" data-color="#ffe4e1" style="background: #ffe4e1;"></button>
                                <button class="color-btn" data-color="#e1f5fe" style="background: #e1f5fe;"></button>
                                <button class="color-btn" data-color="#f3e5f5" style="background: #f3e5f5;"></button>
                            </div>
                        </div>
                        
                        <div class="control-group">
                            <label>气泡类型</label>
                            <select id="bubble-type" class="select-input">
                                <option value="speech">对话气泡</option>
                                <option value="thought">思考气泡</option>
                                <option value="shout">喊叫气泡</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label>尾巴位置</label>
                            <select id="tail-position" class="select-input">
                                <option value="bottom-left">左下角</option>
                                <option value="bottom-right">右下角</option>
                                <option value="top-left">左上角</option>
                                <option value="top-right">右上角</option>
                                <option value="left">左侧</option>
                                <option value="right">右侧</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label>边框样式</label>
                            <select id="border-style" class="select-input">
                                <option value="solid">实线</option>
                                <option value="dashed">虚线</option>
                                <option value="dotted">点线</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label>字体选择</label>
                            <select id="font-family" class="select-input">
                                <option value="沐瑶软笔手写体">沐瑶软笔手写体</option>
                                <option value="包图小白体">包图小白体</option>
                                <option value="思源柔黑">思源柔黑</option>
                                <option value="清松手寫體1">清松手寫體1</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label>气泡透明度</label>
                            <div class="slider-container">
                                <input type="range" id="bubble-opacity" class="slider" min="0.1" max="1" step="0.1" value="0.9">
                                <span id="opacity-value" class="slider-value">90%</span>
                            </div>
                        </div>

                        <div class="control-group">
                            <label>自动裁切边缘</label>
                            <div class="slider-container">
                                <input type="range" id="crop-pixels" class="slider" min="0" max="200" step="10" value="0">
                                <span id="crop-value" class="slider-value">0px</span>
                            </div>
                            <div class="crop-info">
                                <small>裁切四边的像素数量，0表示不裁切</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="panel-section">
                    <h4>操作</h4>
                    <div class="action-buttons">
                        <button id="auto-resize-btn" class="btn btn-info" disabled>
                            <span class="btn-icon">📏</span>
                            自动调整大小
                        </button>
                        <button id="delete-selected-btn" class="btn btn-danger" disabled>
                            <span class="btn-icon">🗑️</span>
                            删除选中
                        </button>
                        <button id="clear-all-btn" class="btn btn-warning" disabled>
                            <span class="btn-icon">🧹</span>
                            清空所有气泡
                        </button>
                    </div>
                </div>
                
                <div class="panel-section">
                    <h4>帮助</h4>
                    <div class="help-content">
                        <ul>
                            <li>点击气泡可以选中并拖动</li>
                            <li>双击气泡可以编辑文字</li>
                            <li>按Delete键删除选中的气泡</li>
                            <li>支持多选操作（Ctrl+点击）</li>
                        </ul>
                    </div>
                </div>
            </aside>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div class="status-info">
                <span id="status-text">准备就绪</span>
            </div>
            <div class="status-info">
                <span id="canvas-info">画布: 未加载</span>
            </div>
        </footer>
    </div>

    <!-- 加载脚本 -->
    <script src="js/font-manager.js"></script>
    <script src="js/config-manager.js"></script>
    <script src="js/viewport-manager.js"></script>
    <script src="js/image-scaler.js"></script>
    <script src="js/speech-bubble.js"></script>
    <script src="js/canvas-manager.js"></script>
    <script src="js/file-manager.js"></script>
    <script src="js/export-manager.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
