/**
 * 文件管理器 - 处理图片文件的导入和相关操作
 */
class FileManager {
    constructor() {
        this.supportedFormats = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        
        console.log('文件管理器初始化完成');
    }
    
    /**
     * 选择图片文件
     */
    async selectImageFile() {
        try {
            // 使用Electron的文件对话框
            const result = await window.electronAPI.showOpenDialog();
            
            if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
                return { success: false, message: '用户取消了文件选择' };
            }
            
            const filePath = result.filePaths[0];
            console.log('选择的文件路径:', filePath);
            
            // 验证文件
            const validation = this.validateImageFile(filePath);
            if (!validation.valid) {
                return { success: false, message: validation.message };
            }
            
            // 读取文件并转换为数据URL
            const imageUrl = await this.loadImageAsDataURL(filePath);
            
            return {
                success: true,
                filePath: filePath,
                imageUrl: imageUrl,
                fileName: window.electronAPI.path.basename(filePath)
            };
            
        } catch (error) {
            console.error('选择文件时出错:', error);
            return { 
                success: false, 
                message: '选择文件时出错: ' + error.message 
            };
        }
    }
    
    /**
     * 验证图片文件
     */
    validateImageFile(filePath) {
        try {
            // 检查electronAPI是否可用
            if (!window.electronAPI || !window.electronAPI.path) {
                console.error('electronAPI不可用');
                return {
                    valid: false,
                    message: '系统错误：文件API不可用'
                };
            }

            // 检查文件路径
            if (!filePath || typeof filePath !== 'string') {
                return {
                    valid: false,
                    message: '无效的文件路径'
                };
            }

            // 检查文件扩展名
            const extension = window.electronAPI.path.extname(filePath).toLowerCase().slice(1);

            if (!extension) {
                return {
                    valid: false,
                    message: '文件没有扩展名'
                };
            }

            if (!this.supportedFormats.includes(extension)) {
                return {
                    valid: false,
                    message: `不支持的文件格式: ${extension}。支持的格式: ${this.supportedFormats.join(', ')}`
                };
            }

            return { valid: true };

        } catch (error) {
            console.error('文件验证错误:', error);
            return {
                valid: false,
                message: '文件验证失败: ' + error.message
            };
        }
    }
    
    /**
     * 将图片文件加载为Data URL
     */
    async loadImageAsDataURL(filePath) {
        try {
            // 读取文件为base64
            const base64Data = await window.electronAPI.readFile(filePath);
            
            // 获取文件扩展名以确定MIME类型
            const extension = window.electronAPI.path.extname(filePath).toLowerCase().slice(1);
            const mimeType = this.getMimeType(extension);
            
            // 构造Data URL
            const dataURL = `data:${mimeType};base64,${base64Data}`;
            
            // 验证图片是否可以加载
            await this.validateImageLoad(dataURL);
            
            return dataURL;
            
        } catch (error) {
            throw new Error('加载图片失败: ' + error.message);
        }
    }
    
    /**
     * 根据文件扩展名获取MIME类型
     */
    getMimeType(extension) {
        const mimeTypes = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'webp': 'image/webp'
        };
        
        return mimeTypes[extension] || 'image/jpeg';
    }
    
    /**
     * 验证图片是否可以正常加载
     */
    validateImageLoad(dataURL) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            
            img.onload = () => {
                // 检查图片尺寸是否合理
                if (img.width < 1 || img.height < 1) {
                    reject(new Error('图片尺寸无效'));
                    return;
                }
                
                if (img.width > 5000 || img.height > 5000) {
                    reject(new Error('图片尺寸过大，请选择较小的图片'));
                    return;
                }
                
                resolve({
                    width: img.width,
                    height: img.height
                });
            };
            
            img.onerror = () => {
                reject(new Error('图片格式不正确或文件已损坏'));
            };
            
            // 设置超时
            setTimeout(() => {
                reject(new Error('图片加载超时'));
            }, 10000);
            
            img.src = dataURL;
        });
    }
    
    /**
     * 获取文件信息
     */
    getFileInfo(filePath) {
        try {
            return {
                name: window.electronAPI.path.basename(filePath),
                extension: window.electronAPI.path.extname(filePath),
                path: filePath
            };
        } catch (error) {
            console.error('获取文件信息失败:', error);
            return null;
        }
    }
    
    /**
     * 检查文件是否为支持的图片格式
     */
    isImageFile(filePath) {
        try {
            const extension = window.electronAPI.path.extname(filePath).toLowerCase().slice(1);
            return this.supportedFormats.includes(extension);
        } catch (error) {
            return false;
        }
    }
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 获取支持的文件格式列表
     */
    getSupportedFormats() {
        return [...this.supportedFormats];
    }
    
    /**
     * 设置最大文件大小限制
     */
    setMaxFileSize(sizeInBytes) {
        this.maxFileSize = sizeInBytes;
    }
    
    /**
     * 获取最大文件大小限制
     */
    getMaxFileSize() {
        return this.maxFileSize;
    }
}
