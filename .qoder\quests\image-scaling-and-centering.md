# 图片缩放和居中功能设计

## 1. 概述

本设计文档针对漫画风格对话气泡编辑器中的图片导入功能进行优化，实现图片按编辑窗口比例智能缩放（仅缩小不放大）和居中显示的功能。

### 核心需求
- 导入的图片按编辑窗口比例自动缩放
- 只缩小不放大，保持图片原始质量
- 图片在编辑窗口中居中显示
- 保持图片原始宽高比

## 2. 技术架构

### 2.1 架构图

```mermaid
graph TD
    A[图片导入] --> B[图片尺寸检测]
    B --> C[窗口尺寸获取]
    C --> D[缩放比例计算]
    D --> E{是否需要缩放}
    E -->|需要缩放| F[应用缩放变换]
    E -->|无需缩放| G[保持原始尺寸]
    F --> H[计算居中位置]
    G --> H
    H --> I[设置图片位置]
    I --> J[更新画布显示]
```

### 2.2 核心组件关系

```mermaid
classDiagram
    class CanvasManager {
        +setBackgroundImage()
        +calculateImageScale()
        +centerImageInViewport()
        +resizeCanvas()
    }
    
    class ImageScaler {
        +calculateFitScale()
        +calculateCenterPosition()
        +validateDimensions()
    }
    
    class ViewportManager {
        +getViewportSize()
        +updateCanvasDisplay()
        +adjustScrollPosition()
    }
    
    CanvasManager --> ImageScaler
    CanvasManager --> ViewportManager
```

## 3. 功能设计

### 3.1 图片缩放策略

| 场景 | 图片尺寸 | 窗口尺寸 | 处理策略 |
|------|----------|----------|----------|
| 图片较小 | 800x600 | 1200x800 | 保持原始尺寸，居中显示 |
| 图片过宽 | 1500x600 | 1200x800 | 按宽度比例缩放 |
| 图片过高 | 800x1200 | 1200x800 | 按高度比例缩放 |
| 图片过大 | 2000x1500 | 1200x800 | 按最小比例缩放 |

### 3.2 缩放算法

```mermaid
flowchart TD
    A[获取图片原始尺寸] --> B[获取可视区域尺寸]
    B --> C[计算宽度比例: viewWidth/imgWidth]
    C --> D[计算高度比例: viewHeight/imgHeight]
    D --> E[选择最小比例: min scale, 1.0]
    E --> F{比例 < 1.0?}
    F -->|是| G[应用缩放]
    F -->|否| H[保持原始尺寸]
    G --> I[计算居中位置]
    H --> I
    I --> J[设置图片变换]
```

### 3.3 居中定位算法

**水平居中计算：**
```
centerX = (viewportWidth - scaledImageWidth) / 2
```

**垂直居中计算：**
```
centerY = (viewportHeight - scaledImageHeight) / 2
```

## 4. 实现方案

### 4.1 CanvasManager 扩展

需要在 `CanvasManager` 类中添加以下方法：

#### 4.1.1 图片缩放计算
- `calculateImageScale(imgWidth, imgHeight, viewWidth, viewHeight)`
- `shouldScaleImage(scale)` 
- `applyImageTransform(img, scale, centerX, centerY)`

#### 4.1.2 视口管理
- `getViewportDimensions()`
- `calculateCenterPosition(imgWidth, imgHeight, viewWidth, viewHeight)`
- `updateCanvasViewport(img)`

#### 4.1.3 背景图片设置优化
对现有的 `setBackgroundImage` 方法进行扩展：
- 添加缩放计算逻辑
- 实现居中定位
- 优化画布容器显示

### 4.2 ViewportManager 新增类

创建专门的视口管理器：
- 监听窗口大小变化
- 计算可用编辑区域
- 管理滚动条显示逻辑

### 4.3 配置项扩展

在用户配置中添加图片显示相关设置：
- `autoScaleImage: true` - 是否自动缩放
- `preventUpscaling: true` - 是否禁止放大
- `centerImage: true` - 是否居中显示
- `maxScaleRatio: 1.0` - 最大缩放比例

## 5. 用户界面优化

### 5.1 画布容器调整

```mermaid
graph LR
    A[Canvas Container] --> B[Flex布局]
    B --> C[居中对齐]
    C --> D[自适应尺寸]
    D --> E[滚动条控制]
```

### 5.2 状态信息显示

在状态栏显示图片信息：
- 原始尺寸：`原始: 1920x1080`
- 显示尺寸：`显示: 960x540 (50%)`
- 位置信息：`位置: 居中`

### 5.3 交互优化

| 操作 | 效果 | 说明 |
|------|------|------|
| 拖拽导入 | 自动缩放居中 | 保持最佳显示效果 |
| 文件选择 | 智能适配 | 根据图片尺寸调整 |
| 窗口调整 | 重新计算 | 动态适应窗口变化 |

## 6. 实现细节

### 6.1 缩放算法实现

```javascript
// 计算适合的缩放比例
calculateFitScale(imgWidth, imgHeight, containerWidth, containerHeight) {
    const scaleX = containerWidth / imgWidth;
    const scaleY = containerHeight / imgHeight;
    
    // 选择最小的缩放比例，确保图片完全显示
    const scale = Math.min(scaleX, scaleY, 1.0);
    
    return scale;
}
```

### 6.2 居中位置计算

```javascript
// 计算居中位置
calculateCenterPosition(imgWidth, imgHeight, scale, containerWidth, containerHeight) {
    const scaledWidth = imgWidth * scale;
    const scaledHeight = imgHeight * scale;
    
    const centerX = (containerWidth - scaledWidth) / 2;
    const centerY = (containerHeight - scaledHeight) / 2;
    
    return { x: centerX, y: centerY };
}
```

### 6.3 响应式处理

```javascript
// 窗口大小变化处理
handleWindowResize() {
    if (this.backgroundImage) {
        const viewport = this.getViewportDimensions();
        const scale = this.calculateImageScale(
            this.backgroundImage.width,
            this.backgroundImage.height,
            viewport.width,
            viewport.height
        );
        
        this.updateImageDisplay(scale, viewport);
    }
}
```

## 7. 测试策略

### 7.1 功能测试用例

| 测试场景 | 测试数据 | 预期结果 |
|----------|----------|----------|
| 小图片导入 | 400x300图片 | 原始尺寸，居中显示 |
| 大图片导入 | 2000x1500图片 | 等比缩放，居中显示 |
| 超宽图片 | 3000x500图片 | 按宽度缩放，居中显示 |
| 超高图片 | 500x2000图片 | 按高度缩放，居中显示 |
| 窗口调整 | 改变窗口大小 | 图片重新计算位置 |

### 7.2 兼容性测试

- 不同分辨率下的显示效果
- 不同图片格式的处理
- 极端尺寸图片的处理
- 内存占用和性能表现

### 7.3 用户体验测试

- 图片导入流畅度
- 缩放效果自然度
- 居中对齐准确性
- 响应式调整及时性

## 8. 性能优化

### 8.1 图片处理优化

- 使用 `requestAnimationFrame` 进行平滑更新
- 避免频繁的DOM操作
- 缓存计算结果减少重复计算

### 8.2 内存管理

- 及时释放不需要的图片资源
- 控制画布缓冲区大小
- 优化fabric.js对象创建

### 8.3 渲染优化

- 延迟加载大图片
- 使用合适的图片质量设置
- 避免不必要的重新渲染

## 9. 错误处理

### 9.1 异常情况处理

| 异常情况 | 处理策略 |
|----------|----------|
| 图片加载失败 | 显示错误提示，回退到原有逻辑 |
| 尺寸计算错误 | 使用默认值，记录错误日志 |
| 内存不足 | 降低图片质量，提示用户 |
| 格式不支持 | 明确错误提示，建议解决方案 |

### 9.2 降级策略

当自动缩放功能出现问题时，系统应该能够：
- 回退到原始的图片显示逻辑
- 保持基本的图片导入功能
- 记录错误信息便于调试

## 10. 配置参数

### 10.1 新增配置项

```javascript
imageDisplayConfig: {
    autoScale: true,          // 自动缩放
    preventUpscaling: true,   // 禁止放大
    centerImage: true,        // 居中显示
    maxScale: 1.0,           // 最大缩放比例
    minScale: 0.1,           // 最小缩放比例
    padding: 20              // 边距
}
```

### 10.2 用户可配置选项

- 是否启用自动缩放
- 是否允许图片放大
- 默认居中方式（居中/左上角）
- 缩放边距设置