const { app, BrowserWindow, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// 保持对窗口对象的全局引用，如果不这样做，当JavaScript对象被垃圾回收时，窗口会自动关闭
let mainWindow;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'src/assets/icons/icon.png'),
    title: '漫画对话气泡编辑器',
    show: false // 先不显示，等加载完成后再显示
  });

  // 加载应用的index.html
  mainWindow.loadFile('src/index.html');

  // 当窗口准备好显示时
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 当窗口被关闭时发出
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，通常会把多个window对象存放在一个数组里面，与此同时，你应该删除相应的元素
    mainWindow = null;
  });

  // 开发环境下打开开发者工具
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// Electron会在初始化后并准备创建浏览器窗口时，调用这个函数
app.whenReady().then(createWindow);

// 当全部窗口关闭时退出
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，否则绝大部分应用及其菜单栏会保持激活
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当单击dock图标并且没有其他窗口打开时，通常在应用程序中重新创建一个窗口
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC处理程序 - 文件对话框
ipcMain.handle('show-open-dialog', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    title: '选择图片文件',
    filters: [
      { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] },
      { name: '所有文件', extensions: ['*'] }
    ],
    properties: ['openFile']
  });
  return result;
});

// IPC处理程序 - 保存对话框
ipcMain.handle('show-save-dialog', async () => {
  const result = await dialog.showSaveDialog(mainWindow, {
    title: '保存图片',
    defaultPath: 'comic-with-bubbles.png',
    filters: [
      { name: 'PNG图片', extensions: ['png'] },
      { name: 'JPEG图片', extensions: ['jpg', 'jpeg'] },
      { name: '所有文件', extensions: ['*'] }
    ]
  });
  return result;
});

// IPC处理程序 - 读取文件
ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const data = fs.readFileSync(filePath);
    return data.toString('base64');
  } catch (error) {
    throw new Error(`读取文件失败: ${error.message}`);
  }
});

// IPC处理程序 - 写入文件
ipcMain.handle('write-file', async (event, filePath, data) => {
  try {
    // 将base64数据转换为buffer
    const buffer = Buffer.from(data.replace(/^data:image\/\w+;base64,/, ''), 'base64');
    fs.writeFileSync(filePath, buffer);
    return { success: true };
  } catch (error) {
    throw new Error(`保存文件失败: ${error.message}`);
  }
});

// IPC处理程序 - 读取fonts目录
ipcMain.handle('read-fonts-directory', async (event) => {
  try {
    const fontsPath = path.join(__dirname, 'src', 'fonts');
    console.log('读取字体目录:', fontsPath);

    if (!fs.existsSync(fontsPath)) {
      console.warn('字体目录不存在:', fontsPath);
      return [];
    }

    const files = fs.readdirSync(fontsPath);
    const fontFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.ttf', '.otf', '.woff', '.woff2'].includes(ext);
    });

    console.log('发现字体文件:', fontFiles);
    return fontFiles;
  } catch (error) {
    console.error('读取字体目录失败:', error);
    return [];
  }
});

// IPC处理程序 - 加载配置文件
ipcMain.handle('load-config', async (event, configFile) => {
  try {
    const configPath = path.join(__dirname, configFile);
    console.log('加载配置文件:', configPath);

    if (!fs.existsSync(configPath)) {
      console.log('配置文件不存在:', configPath);
      return null;
    }

    const configData = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configData);
    console.log('配置文件加载成功');
    return config;
  } catch (error) {
    console.error('加载配置文件失败:', error);
    return null;
  }
});

// IPC处理程序 - 保存配置文件
ipcMain.handle('save-config', async (event, configFile, config) => {
  try {
    const configPath = path.join(__dirname, configFile);
    console.log('保存配置文件:', configPath);

    const configData = JSON.stringify(config, null, 2);
    fs.writeFileSync(configPath, configData, 'utf8');
    console.log('配置文件保存成功');
    return { success: true };
  } catch (error) {
    console.error('保存配置文件失败:', error);
    throw new Error(`保存配置失败: ${error.message}`);
  }
});
