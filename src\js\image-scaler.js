/**
 * 图片缩放器 - 负责图片缩放计算和居中定位
 */
class ImageScaler {
    constructor(config = {}) {
        this.config = {
            autoScale: true,
            preventUpscaling: true,
            centerImage: true,
            maxScale: 1.0,
            minScale: 0.1,
            padding: 20,
            maintainAspectRatio: true,
            ...config
        };
    }
    
    /**
     * 计算适合的缩放比例
     * @param {number} imgWidth - 图片宽度
     * @param {number} imgHeight - 图片高度
     * @param {number} containerWidth - 容器宽度
     * @param {number} containerHeight - 容器高度
     * @returns {number} 缩放比例
     */
    calculateFitScale(imgWidth, imgHeight, containerWidth, containerHeight) {
        try {
            // 验证输入参数
            if (!this.validateDimensions(imgWidth, imgHeight, containerWidth, containerHeight)) {
                console.warn('图片或容器尺寸无效，使用默认缩放比例');
                return 1.0;
            }
            
            // 如果不启用自动缩放，直接返回1.0
            if (!this.config.autoScale) {
                return 1.0;
            }
            
            // 计算可用空间（减去边距）
            const availableWidth = Math.max(containerWidth - (this.config.padding * 2), 100);
            const availableHeight = Math.max(containerHeight - (this.config.padding * 2), 100);
            
            // 计算宽度和高度的缩放比例
            const scaleX = availableWidth / imgWidth;
            const scaleY = availableHeight / imgHeight;
            
            // 选择最小的缩放比例，确保图片完全显示
            let scale = Math.min(scaleX, scaleY);
            
            // 如果禁止放大，限制最大缩放比例
            if (this.config.preventUpscaling) {
                scale = Math.min(scale, this.config.maxScale);
            }
            
            // 应用缩放范围限制
            scale = Math.max(this.config.minScale, Math.min(this.config.maxScale, scale));
            
            console.log(`缩放计算: 图片${imgWidth}x${imgHeight}, 容器${containerWidth}x${containerHeight}, 缩放比例${scale.toFixed(3)}`);
            
            return scale;
            
        } catch (error) {
            console.error('计算缩放比例失败:', error);
            return 1.0;
        }
    }
    
    /**
     * 计算居中位置
     * @param {number} imgWidth - 图片宽度
     * @param {number} imgHeight - 图片高度
     * @param {number} scale - 缩放比例
     * @param {number} containerWidth - 容器宽度
     * @param {number} containerHeight - 容器高度
     * @returns {Object} 居中位置坐标
     */
    calculateCenterPosition(imgWidth, imgHeight, scale, containerWidth, containerHeight) {
        try {
            // 验证输入参数
            if (!this.validateDimensions(imgWidth, imgHeight, containerWidth, containerHeight) || !scale) {
                console.warn('计算居中位置时参数无效');
                return { x: 0, y: 0 };
            }
            
            // 如果不启用居中，返回左上角位置
            if (!this.config.centerImage) {
                return { x: 0, y: 0 };
            }
            
            // 计算缩放后的图片尺寸
            const scaledWidth = imgWidth * scale;
            const scaledHeight = imgHeight * scale;
            
            // 计算居中位置
            const centerX = Math.max(0, (containerWidth - scaledWidth) / 2);
            const centerY = Math.max(0, (containerHeight - scaledHeight) / 2);
            
            console.log(`居中计算: 缩放后${scaledWidth.toFixed(1)}x${scaledHeight.toFixed(1)}, 位置(${centerX.toFixed(1)}, ${centerY.toFixed(1)})`);

            return {
                x: centerX,
                y: centerY
            };

        } catch (error) {
            console.error('计算居中位置失败:', error);
            return { x: 0, y: 0 };
        }
    }\n    \n    /**\n     * 计算完整的图片变换信息\n     * @param {number} imgWidth - 图片宽度\n     * @param {number} imgHeight - 图片高度\n     * @param {number} containerWidth - 容器宽度\n     * @param {number} containerHeight - 容器高度\n     * @returns {Object} 完整的变换信息\n     */\n    calculateImageTransform(imgWidth, imgHeight, containerWidth, containerHeight) {\n        try {\n            // 计算缩放比例\n            const scale = this.calculateFitScale(imgWidth, imgHeight, containerWidth, containerHeight);\n            \n            // 计算居中位置\n            const position = this.calculateCenterPosition(imgWidth, imgHeight, scale, containerWidth, containerHeight);\n            \n            // 计算缩放后的尺寸\n            const scaledWidth = imgWidth * scale;\n            const scaledHeight = imgHeight * scale;\n            \n            // 判断图片显示状态\n            const isScaled = Math.abs(scale - 1.0) > 0.001;\n            const isFitToContainer = scaledWidth <= containerWidth && scaledHeight <= containerHeight;\n            \n            return {\n                scale,\n                position,\n                originalSize: {\n                    width: imgWidth,\n                    height: imgHeight\n                },\n                scaledSize: {\n                    width: scaledWidth,\n                    height: scaledHeight\n                },\n                isScaled,\n                isFitToContainer,\n                needsScrollbars: !isFitToContainer\n            };\n            \n        } catch (error) {\n            console.error('计算图片变换失败:', error);\n            return {\n                scale: 1.0,\n                position: { x: 0, y: 0 },\n                originalSize: { width: imgWidth, height: imgHeight },\n                scaledSize: { width: imgWidth, height: imgHeight },\n                isScaled: false,\n                isFitToContainer: false,\n                needsScrollbars: false\n            };\n        }\n    }\n    \n    /**\n     * 检查是否需要缩放\n     * @param {number} scale - 缩放比例\n     * @returns {boolean} 是否需要缩放\n     */\n    shouldScaleImage(scale) {\n        return Math.abs(scale - 1.0) > 0.001;\n    }\n    \n    /**\n     * 验证尺寸参数的有效性\n     * @param {number} imgWidth - 图片宽度\n     * @param {number} imgHeight - 图片高度\n     * @param {number} containerWidth - 容器宽度\n     * @param {number} containerHeight - 容器高度\n     * @returns {boolean} 参数是否有效\n     */\n    validateDimensions(imgWidth, imgHeight, containerWidth, containerHeight) {\n        return (\n            typeof imgWidth === 'number' && imgWidth > 0 &&\n            typeof imgHeight === 'number' && imgHeight > 0 &&\n            typeof containerWidth === 'number' && containerWidth > 0 &&\n            typeof containerHeight === 'number' && containerHeight > 0\n        );\n    }\n    \n    /**\n     * 更新配置\n     * @param {Object} newConfig - 新配置\n     */\n    updateConfig(newConfig) {\n        this.config = {\n            ...this.config,\n            ...newConfig\n        };\n        \n        console.log('图片缩放器配置已更新:', this.config);\n    }\n    \n    /**\n     * 获取当前配置\n     * @returns {Object} 当前配置\n     */\n    getConfig() {\n        return { ...this.config };\n    }\n    \n    /**\n     * 重置为默认配置\n     */\n    resetConfig() {\n        this.config = {\n            autoScale: true,\n            preventUpscaling: true,\n            centerImage: true,\n            maxScale: 1.0,\n            minScale: 0.1,\n            padding: 20,\n            maintainAspectRatio: true\n        };\n        \n        console.log('图片缩放器配置已重置为默认值');\n    }\n    \n    /**\n     * 生成状态报告\n     * @param {Object} transform - 变换信息\n     * @returns {string} 状态报告\n     */\n    generateStatusReport(transform) {\n        const { originalSize, scaledSize, scale, isScaled } = transform;\n        \n        let status = `原始: ${originalSize.width}x${originalSize.height}`;\n        \n        if (isScaled) {\n            const percentage = Math.round(scale * 100);\n            status += ` | 显示: ${Math.round(scaledSize.width)}x${Math.round(scaledSize.height)} (${percentage}%)`;\n        } else {\n            status += ' | 显示: 原始尺寸';\n        }\n        \n        if (this.config.centerImage) {\n            status += ' | 居中';\n        }\n        \n        return status;\n    }\n}\n\n// 导出图片缩放器\nwindow.ImageScaler = ImageScaler;