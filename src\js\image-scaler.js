/**
 * 图片缩放器 - 负责图片缩放计算和居中定位
 */
class ImageScaler {
    constructor(config = {}) {
        this.config = {
            autoScale: true,
            preventUpscaling: true,
            centerImage: true,
            maxScale: 1.0,
            minScale: 0.1,
            padding: 20,
            maintainAspectRatio: true,
            ...config
        };
    }
    
    /**
     * 计算适合的缩放比例
     * @param {number} imgWidth - 图片宽度
     * @param {number} imgHeight - 图片高度
     * @param {number} containerWidth - 容器宽度
     * @param {number} containerHeight - 容器高度
     * @returns {number} 缩放比例
     */
    calculateFitScale(imgWidth, imgHeight, containerWidth, containerHeight) {
        try {
            // 验证输入参数
            if (!this.validateDimensions(imgWidth, imgHeight, containerWidth, containerHeight)) {
                console.warn('图片或容器尺寸无效，使用默认缩放比例');
                return 1.0;
            }
            
            // 如果不启用自动缩放，直接返回1.0
            if (!this.config.autoScale) {
                return 1.0;
            }
            
            // 计算可用空间（减去边距）
            const availableWidth = Math.max(containerWidth - (this.config.padding * 2), 100);
            const availableHeight = Math.max(containerHeight - (this.config.padding * 2), 100);
            
            // 计算宽度和高度的缩放比例
            const scaleX = availableWidth / imgWidth;
            const scaleY = availableHeight / imgHeight;
            
            // 选择最小的缩放比例，确保图片完全显示
            let scale = Math.min(scaleX, scaleY);
            
            // 如果禁止放大，限制最大缩放比例
            if (this.config.preventUpscaling) {
                scale = Math.min(scale, this.config.maxScale);
            }
            
            // 应用缩放范围限制
            scale = Math.max(this.config.minScale, Math.min(this.config.maxScale, scale));
            
            console.log(`缩放计算: 图片${imgWidth}x${imgHeight}, 容器${containerWidth}x${containerHeight}, 缩放比例${scale.toFixed(3)}`);
            
            return scale;
            
        } catch (error) {
            console.error('计算缩放比例失败:', error);
            return 1.0;
        }
    }
    
    /**
     * 计算居中位置
     * @param {number} imgWidth - 图片宽度
     * @param {number} imgHeight - 图片高度
     * @param {number} scale - 缩放比例
     * @param {number} containerWidth - 容器宽度
     * @param {number} containerHeight - 容器高度
     * @returns {Object} 居中位置坐标
     */
    calculateCenterPosition(imgWidth, imgHeight, scale, containerWidth, containerHeight) {
        try {
            // 验证输入参数
            if (!this.validateDimensions(imgWidth, imgHeight, containerWidth, containerHeight) || !scale) {
                console.warn('计算居中位置时参数无效');
                return { x: 0, y: 0 };
            }
            
            // 如果不启用居中，返回左上角位置
            if (!this.config.centerImage) {
                return { x: 0, y: 0 };
            }
            
            // 计算缩放后的图片尺寸
            const scaledWidth = imgWidth * scale;
            const scaledHeight = imgHeight * scale;
            
            // 计算居中位置
            const centerX = Math.max(0, (containerWidth - scaledWidth) / 2);
            const centerY = Math.max(0, (containerHeight - scaledHeight) / 2);
            
            console.log(`居中计算: 缩放后${scaledWidth.toFixed(1)}x${scaledHeight.toFixed(1)}, 位置(${centerX.toFixed(1)}, ${centerY.toFixed(1)})`);

            return {
                x: centerX,
                y: centerY
            };

        } catch (error) {
            console.error('计算居中位置失败:', error);
            return { x: 0, y: 0 };
        }
    }

    /**
     * 计算完整的图片变换信息
     * @param {number} imgWidth - 图片宽度
     * @param {number} imgHeight - 图片高度
     * @param {number} containerWidth - 容器宽度
     * @param {number} containerHeight - 容器高度
     * @returns {Object} 完整的变换信息
     */
    calculateImageTransform(imgWidth, imgHeight, containerWidth, containerHeight) {
        try {
            // 计算缩放比例
            const scale = this.calculateFitScale(imgWidth, imgHeight, containerWidth, containerHeight);

            // 计算居中位置
            const position = this.calculateCenterPosition(imgWidth, imgHeight, scale, containerWidth, containerHeight);

            // 计算缩放后的尺寸
            const scaledWidth = imgWidth * scale;
            const scaledHeight = imgHeight * scale;

            // 判断图片显示状态
            const isScaled = Math.abs(scale - 1.0) > 0.001;
            const isFitToContainer = scaledWidth <= containerWidth && scaledHeight <= containerHeight;

            return {
                scale,
                position,
                originalSize: {
                    width: imgWidth,
                    height: imgHeight
                },
                scaledSize: {
                    width: scaledWidth,
                    height: scaledHeight
                },
                isScaled,
                isFitToContainer,
                needsScrollbars: !isFitToContainer
            };

        } catch (error) {
            console.error('计算图片变换失败:', error);
            return {
                scale: 1.0,
                position: { x: 0, y: 0 },
                originalSize: { width: imgWidth, height: imgHeight },
                scaledSize: { width: imgWidth, height: imgHeight },
                isScaled: false,
                isFitToContainer: false,
                needsScrollbars: false
            };
        }
    }

    /**
     * 检查是否需要缩放
     * @param {number} scale - 缩放比例
     * @returns {boolean} 是否需要缩放
     */
    shouldScaleImage(scale) {
        return Math.abs(scale - 1.0) > 0.001;
    }

    /**
     * 验证尺寸参数的有效性
     * @param {number} imgWidth - 图片宽度
     * @param {number} imgHeight - 图片高度
     * @param {number} containerWidth - 容器宽度
     * @param {number} containerHeight - 容器高度
     * @returns {boolean} 参数是否有效
     */
    validateDimensions(imgWidth, imgHeight, containerWidth, containerHeight) {
        return (
            typeof imgWidth === 'number' && imgWidth > 0 &&
            typeof imgHeight === 'number' && imgHeight > 0 &&
            typeof containerWidth === 'number' && containerWidth > 0 &&
            typeof containerHeight === 'number' && containerHeight > 0
        );
    }

    /**
     * 更新配置
     * @param {Object} newConfig - 新配置
     */
    updateConfig(newConfig) {
        this.config = {
            ...this.config,
            ...newConfig
        };

        console.log('图片缩放器配置已更新:', this.config);
    }

    /**
     * 获取当前配置
     * @returns {Object} 当前配置
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * 重置为默认配置
     */
    resetConfig() {
        this.config = {
            autoScale: true,
            preventUpscaling: true,
            centerImage: true,
            maxScale: 1.0,
            minScale: 0.1,
            padding: 20,
            maintainAspectRatio: true
        };

        console.log('图片缩放器配置已重置为默认值');
    }

    /**
     * 生成状态报告
     * @param {Object} transform - 变换信息
     * @returns {string} 状态报告
     */
    generateStatusReport(transform) {
        const { originalSize, scaledSize, scale, isScaled } = transform;

        let status = `原始: ${originalSize.width}x${originalSize.height}`;

        if (isScaled) {
            const percentage = Math.round(scale * 100);
            status += ` | 显示: ${Math.round(scaledSize.width)}x${Math.round(scaledSize.height)} (${percentage}%)`;
        } else {
            status += ' | 显示: 原始尺寸';
        }

        if (this.config.centerImage) {
            status += ' | 居中';
        }

        return status;
    }
}

// 导出图片缩放器
window.ImageScaler = ImageScaler;