language: node_js

node_js:
  - "12"

addons:
  apt:
    packages:
    - libgif-dev
    - libpng-dev
    - libpango1.0-dev
    - libjpeg8-dev
    - librsvg2-dev
    - libcairo2-dev

stages:
  - Linting and Building
  - Unit Tests
  - Visual Tests
  - Extra OS

cache:
  directories:
    - node_modules

env:
  global:
    - LAUNCHER=Node

jobs:
  fast_finish: true
  allow_failures:
    - env: CANFAIL=TRUE
    - env: LAUNCHER=Node CANFAIL=TRUE
  include:
    - stage: Linting and Building
      env: STEP=LINT
      install: npm install eslint@4.7.x
      script: 'npm run lint && npm run lint_tests'
      addons:
        apt:
          packages: # avoid installing packages
    - stage: Linting and Building
      env: STEP=BUILD
      install: npm install uglify-js@3.3.x
      script: 'npm run build'
      addons:
        apt:
          packages: # avoid installing packages
    - stage: Unit Tests
      env: LAUNCHER=Chrome
      script: npm run build:fast && testem ci --port 8080 -f testem.json -l $LAUNCHER
      addons:
        apt:
          packages: # avoid installing packages
    - stage: Unit Tests
      env: LAUNCHER=Firefox
      script: npm run build:fast && testem ci --port 8080 -f testem.json -l $LAUNCHER
      addons:
        apt:
          packages: # avoid installing packages
    - stage: Unit Tests
      env: LAUNCHER=Node
      node_js: "14"
    - stage: Unit Tests
      env: LAUNCHER=Node
      node_js: "12"
    - stage: Visual Tests
      env: LAUNCHER=Node
      node_js: "12"
      script: npm run build:fast && npm run test:visual
    - stage: Visual Tests
      env: LAUNCHER=Chrome
      script: npm run build:fast && testem ci --port 8080 -f testem-visual.json -l $LAUNCHER
      addons:
        apt:
          packages: # avoid installing packages
    - stage: Visual Tests
      env: LAUNCHER=Firefox
      script: npm run build:fast && testem ci --port 8080 -f testem-visual.json -l $LAUNCHER
      addons:
        apt:
          packages: # avoid installing packages
    # - stage: Extra OS
    #   env: CANFAIL=TRUE
    #   node_js: "10"
    #   script: npm run build:fast && npm run test:visual
    #   before_install: brew upgrade giflib && brew install pkg-config cairo pango libpng jpeg librsvg
    #   os: osx
    # - stage: Extra OS
    #   env: CANFAIL=TRUE
    #   node_js: "10"
    #   script: npm run build:fast && npm run test:visual
    #   os: windows
script: npm run build:fast && npm run test

dist: bionic
