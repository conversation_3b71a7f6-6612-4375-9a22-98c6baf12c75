<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体与透明度功能演示</title>
    <style>
        /* 加载字体 */
        @font-face {
            font-family: '沐瑶软笔手写体';
            src: url('src/fonts/沐瑶软笔手写体.ttf') format('truetype');
        }
        @font-face {
            font-family: '包图小白体';
            src: url('src/fonts/包图小白体.ttf') format('truetype');
        }
        @font-face {
            font-family: '思源柔黑';
            src: url('src/fonts/思源柔黑.ttf') format('truetype');
        }
        @font-face {
            font-family: '清松手寫體1';
            src: url('src/fonts/清松手寫體1.ttf') format('truetype');
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }
        
        .demo-section h2 {
            color: #667eea;
            margin-bottom: 20px;
            border-bottom: 2px solid #764ba2;
            padding-bottom: 10px;
        }
        
        .font-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .font-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .font-card h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }
        
        .bubble-sample {
            position: relative;
            background: white;
            border: 3px solid #667eea;
            padding: 12px 20px;
            margin: 15px auto;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 60px;
            max-width: 200px;
            text-align: center;
            line-height: 1.2;
            box-shadow: 3px 3px 0 rgba(102,126,234,0.3);
        }
        
        .bubble-sample::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 30%;
            width: 0;
            height: 0;
            border: 10px solid transparent;
            border-top: 15px solid white;
            border-bottom: none;
            transform: translateX(-50%);
        }
        
        .bubble-sample::before {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 30%;
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-top: 17px solid #667eea;
            border-bottom: none;
            transform: translateX(-50%);
        }
        
        .font-muyao { font-family: '沐瑶软笔手写体'; }
        .font-baotu { font-family: '包图小白体'; }
        .font-siyuan { font-family: '思源柔黑'; }
        .font-qingsong { font-family: '清松手寫體1'; }
        
        .opacity-demo {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        
        .opacity-sample {
            text-align: center;
        }
        
        .opacity-100 { opacity: 1.0; }
        .opacity-90 { opacity: 0.9; }
        .opacity-70 { opacity: 0.7; }
        .opacity-50 { opacity: 0.5; }
        .opacity-30 { opacity: 0.3; }
        
        .feature-list {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .feature-list h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .feature-list ul {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            margin: 8px 0;
            padding: 8px 12px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #764ba2;
        }
        
        .config-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .config-info strong {
            color: #856404;
        }
        
        .interface-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .interface-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .interface-card h4 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .ui-text {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        .bubble-text {
            font-family: '沐瑶软笔手写体';
            font-size: 16px;
            color: #333;
            text-align: center;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .highlight-box h3 {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 字体与透明度功能</h1>
        
        <div class="demo-section">
            <h2>📝 自定义字体展示</h2>
            <p>应用现在会自动读取 <code>src/fonts/</code> 文件夹中的所有字体文件，并在气泡中使用：</p>
            
            <div class="font-showcase">
                <div class="font-card">
                    <h3>沐瑶软笔手写体（默认）</h3>
                    <div class="bubble-sample font-muyao">
                        你好，世界！<br>这是手写体
                    </div>
                </div>
                
                <div class="font-card">
                    <h3>包图小白体</h3>
                    <div class="bubble-sample font-baotu">
                        清新可爱<br>小白体风格
                    </div>
                </div>
                
                <div class="font-card">
                    <h3>思源柔黑</h3>
                    <div class="bubble-sample font-siyuan">
                        优雅柔和<br>现代设计
                    </div>
                </div>
                
                <div class="font-card">
                    <h3>清松手寫體1</h3>
                    <div class="bubble-sample font-qingsong">
                        传统手写<br>古典韵味
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🌟 透明度控制</h2>
            <p>气泡透明度可以从10%到100%自由调节，默认为90%：</p>
            
            <div class="opacity-demo">
                <div class="opacity-sample">
                    <div class="bubble-sample font-muyao opacity-100">
                        100%不透明
                    </div>
                    <p>100%</p>
                </div>
                
                <div class="opacity-sample">
                    <div class="bubble-sample font-muyao opacity-90">
                        90%透明度<br>（默认）
                    </div>
                    <p>90%（推荐）</p>
                </div>
                
                <div class="opacity-sample">
                    <div class="bubble-sample font-muyao opacity-70">
                        70%透明度
                    </div>
                    <p>70%</p>
                </div>
                
                <div class="opacity-sample">
                    <div class="bubble-sample font-muyao opacity-50">
                        50%透明度
                    </div>
                    <p>50%</p>
                </div>
                
                <div class="opacity-sample">
                    <div class="bubble-sample font-muyao opacity-30">
                        30%透明度
                    </div>
                    <p>30%</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🔧 智能字体管理</h2>
            <div class="interface-comparison">
                <div class="interface-card">
                    <h4>✅ 界面文字（系统字体）</h4>
                    <div class="ui-text">
                        • 字体大小：16px<br>
                        • 气泡颜色：白色<br>
                        • 边框样式：实线<br>
                        • 气泡类型：对话气泡<br>
                        • 尾巴位置：左下角
                    </div>
                </div>
                
                <div class="interface-card">
                    <h4>🎨 气泡文字（自定义字体）</h4>
                    <div class="bubble-text">
                        哇，天好晚了哎<br>
                        好像回不去宿舍了<br>
                        这是沐瑶软笔手写体
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>⚙️ 配置管理系统</h2>
            <div class="feature-list">
                <h3>新增功能特性</h3>
                <ul>
                    <li><strong>自动字体发现</strong> - 启动时扫描fonts文件夹</li>
                    <li><strong>配置持久化</strong> - 设置自动保存到user-config.json</li>
                    <li><strong>智能字体分离</strong> - 界面使用系统字体，气泡使用自定义字体</li>
                    <li><strong>透明度控制</strong> - 10%-100%范围，默认90%</li>
                    <li><strong>实时预览</strong> - 设置改变立即生效</li>
                </ul>
            </div>
            
            <div class="config-info">
                <strong>💾 配置文件位置：</strong> user-config.json<br>
                <strong>🎨 字体文件夹：</strong> src/fonts/<br>
                <strong>🔄 自动保存：</strong> 设置改变时立即保存<br>
                <strong>📱 跨平台：</strong> 支持Windows、macOS、Linux
            </div>
        </div>
        
        <div class="highlight-box">
            <h3>🎯 立即体验新功能</h3>
            <p>
                现在就打开应用，体验全新的字体和透明度功能！<br>
                • 在字体选择器中切换不同字体<br>
                • 调节透明度滑块看效果<br>
                • 添加新字体到fonts文件夹自动识别<br>
                • 所有设置都会自动保存！
            </p>
        </div>
    </div>
</body>
</html>
