<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初始化调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .log-item {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        #canvas-container {
            width: 800px;
            height: 600px;
            border: 2px solid #007bff;
            margin: 20px 0;
            position: relative;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h2>🔍 初始化调试工具</h2>
        <p>这个页面用于调试 CanvasManager 初始化失败的问题</p>
        
        <button class="test-button" onclick="testDOMReady()">测试 DOM 准备状态</button>
        <button class="test-button" onclick="testElementExists()">测试元素存在性</button>
        <button class="test-button" onclick="testViewportManager()">测试 ViewportManager</button>
        <button class="test-button" onclick="testImageScaler()">测试 ImageScaler</button>
        <button class="test-button" onclick="testCanvasManager()">测试 CanvasManager</button>
        <button class="test-button" onclick="clearLogs()">清空日志</button>
    </div>

    <div class="debug-container">
        <h3>📋 调试日志</h3>
        <div id="debug-logs"></div>
    </div>

    <!-- 模拟画布容器 -->
    <div class="debug-container">
        <h3>🖼️ 画布容器测试</h3>
        <div id="canvas-container">
            <canvas id="main-canvas"></canvas>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script src="../src/js/viewport-manager.js"></script>
    <script src="../src/js/image-scaler.js"></script>
    <script src="../src/js/canvas-manager.js"></script>

    <script>
        // 调试日志函数
        function log(message, type = 'info') {
            const logsContainer = document.getElementById('debug-logs');
            const logItem = document.createElement('div');
            logItem.className = `log-item ${type}`;
            logItem.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsContainer.appendChild(logItem);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('debug-logs').innerHTML = '';
        }

        // 测试函数
        function testDOMReady() {
            log('开始测试 DOM 准备状态', 'info');
            log(`document.readyState: ${document.readyState}`, 'info');
            
            if (document.readyState === 'complete') {
                log('DOM 已完全加载', 'success');
            } else if (document.readyState === 'interactive') {
                log('DOM 加载完成，但资源可能还在加载', 'warning');
            } else {
                log('DOM 还在加载中', 'error');
            }
        }

        function testElementExists() {
            log('开始测试元素存在性', 'info');
            
            const canvasContainer = document.getElementById('canvas-container');
            if (canvasContainer) {
                log('✅ canvas-container 元素存在', 'success');
                log(`容器尺寸: ${canvasContainer.offsetWidth}x${canvasContainer.offsetHeight}`, 'info');
            } else {
                log('❌ canvas-container 元素不存在', 'error');
            }

            const mainCanvas = document.getElementById('main-canvas');
            if (mainCanvas) {
                log('✅ main-canvas 元素存在', 'success');
            } else {
                log('❌ main-canvas 元素不存在', 'error');
            }
        }

        function testViewportManager() {
            log('开始测试 ViewportManager', 'info');
            
            try {
                if (typeof ViewportManager === 'undefined') {
                    log('❌ ViewportManager 类未定义', 'error');
                    return;
                }
                
                log('✅ ViewportManager 类已定义', 'success');
                
                const viewportManager = new ViewportManager();
                log('✅ ViewportManager 实例创建成功', 'success');
                
                // 等待一下让初始化完成
                setTimeout(() => {
                    if (viewportManager.canvasContainer) {
                        log('✅ ViewportManager 找到了 canvas-container', 'success');
                    } else {
                        log('❌ ViewportManager 未找到 canvas-container', 'error');
                    }
                }, 100);
                
            } catch (error) {
                log(`❌ ViewportManager 测试失败: ${error.message}`, 'error');
                console.error('ViewportManager 错误详情:', error);
            }
        }

        function testImageScaler() {
            log('开始测试 ImageScaler', 'info');
            
            try {
                if (typeof ImageScaler === 'undefined') {
                    log('❌ ImageScaler 类未定义', 'error');
                    return;
                }
                
                log('✅ ImageScaler 类已定义', 'success');
                
                const imageScaler = new ImageScaler();
                log('✅ ImageScaler 实例创建成功', 'success');
                
                // 测试基本功能
                const scale = imageScaler.calculateFitScale(1000, 800, 500, 400);
                log(`✅ ImageScaler 计算缩放比例: ${scale}`, 'success');
                
            } catch (error) {
                log(`❌ ImageScaler 测试失败: ${error.message}`, 'error');
                console.error('ImageScaler 错误详情:', error);
            }
        }

        function testCanvasManager() {
            log('开始测试 CanvasManager', 'info');
            
            try {
                if (typeof CanvasManager === 'undefined') {
                    log('❌ CanvasManager 类未定义', 'error');
                    return;
                }
                
                log('✅ CanvasManager 类已定义', 'success');
                
                // 检查 Fabric.js 是否可用
                if (typeof fabric === 'undefined') {
                    log('❌ Fabric.js 未加载', 'error');
                    return;
                }
                log('✅ Fabric.js 已加载', 'success');
                
                const canvasManager = new CanvasManager();
                log('✅ CanvasManager 实例创建成功', 'success');
                
                // 等待初始化完成
                setTimeout(() => {
                    if (canvasManager.viewportManager) {
                        log('✅ CanvasManager 成功创建 ViewportManager', 'success');
                    } else {
                        log('❌ CanvasManager 未能创建 ViewportManager', 'error');
                    }
                    
                    if (canvasManager.imageScaler) {
                        log('✅ CanvasManager 成功创建 ImageScaler', 'success');
                    } else {
                        log('❌ CanvasManager 未能创建 ImageScaler', 'error');
                    }
                    
                    if (canvasManager.canvas) {
                        log('✅ CanvasManager 成功创建 Fabric Canvas', 'success');
                    } else {
                        log('❌ CanvasManager 未能创建 Fabric Canvas', 'error');
                    }
                }, 200);
                
            } catch (error) {
                log(`❌ CanvasManager 测试失败: ${error.message}`, 'error');
                console.error('CanvasManager 错误详情:', error);
            }
        }

        // 页面加载完成后自动运行基本测试
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 调试页面加载完成', 'success');
            setTimeout(() => {
                testDOMReady();
                testElementExists();
            }, 100);
        });
    </script>
</body>
</html>
