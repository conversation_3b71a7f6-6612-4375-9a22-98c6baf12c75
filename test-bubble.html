<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>气泡样式测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .bubble-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }
        .bubble {
            position: relative;
            background: white;
            border: 2.5px solid #000;
            border-radius: 20px;
            padding: 16px;
            max-width: 250px;
            font-size: 16px;
            line-height: 1.3;
            box-shadow: 3px 3px 0 rgba(0,0,0,0.2);
        }
        
        /* 左下角气泡 */
        .bubble-bottom-left::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 25%;
            width: 0;
            height: 0;
            border: 8px solid transparent;
            border-top: 12px solid white;
            border-bottom: none;
        }
        .bubble-bottom-left::before {
            content: '';
            position: absolute;
            bottom: -11px;
            left: calc(25% - 1px);
            width: 0;
            height: 0;
            border: 9px solid transparent;
            border-top: 13px solid #000;
            border-bottom: none;
        }
        
        /* 右下角气泡 */
        .bubble-bottom-right::after {
            content: '';
            position: absolute;
            bottom: -8px;
            right: 25%;
            width: 0;
            height: 0;
            border: 8px solid transparent;
            border-top: 12px solid white;
            border-bottom: none;
        }
        .bubble-bottom-right::before {
            content: '';
            position: absolute;
            bottom: -11px;
            right: calc(25% - 1px);
            width: 0;
            height: 0;
            border: 9px solid transparent;
            border-top: 13px solid #000;
            border-bottom: none;
        }
        
        /* 思考气泡 */
        .bubble-thought {
            border-radius: 30px;
            background: #f0f8ff;
        }
        .bubble-thought::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 30%;
            width: 8px;
            height: 8px;
            background: white;
            border: 2.5px solid #000;
            border-radius: 50%;
        }
        .bubble-thought::before {
            content: '';
            position: absolute;
            bottom: -35px;
            left: 25%;
            width: 6px;
            height: 6px;
            background: white;
            border: 2.5px solid #000;
            border-radius: 50%;
        }
        
        /* 喊叫气泡 */
        .bubble-shout {
            background: #fff3cd;
            border: 3px solid #856404;
            clip-path: polygon(
                8px 0%, calc(100% - 8px) 0%, 100% 8px, 100% calc(100% - 8px),
                calc(100% - 8px) 100%, 8px 100%, 0% calc(100% - 8px), 0% 8px
            );
            border-radius: 0;
        }
        
        .controls {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .control-group {
            margin: 10px 0;
        }
        
        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        
        select, input {
            padding: 5px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>🎨 漫画气泡样式设计</h1>
    
    <div class="controls">
        <div class="control-group">
            <label>气泡类型:</label>
            <select id="bubbleType">
                <option value="speech">对话气泡</option>
                <option value="thought">思考气泡</option>
                <option value="shout">喊叫气泡</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>尾巴位置:</label>
            <select id="tailPosition">
                <option value="bottom-left">左下角</option>
                <option value="bottom-right">右下角</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>文字内容:</label>
            <input type="text" id="bubbleText" value="晚上想去哪里吃饭？" style="width: 200px;">
        </div>
        
        <button onclick="updateBubble()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">更新气泡</button>
    </div>
    
    <div class="bubble-container">
        <div class="bubble bubble-bottom-left" id="previewBubble">
            晚上想去哪里吃饭？
        </div>
    </div>
    
    <h2>📋 样式预览</h2>
    <div class="bubble-container">
        <div class="bubble bubble-bottom-left">
            左下角对话气泡<br>适合左侧角色
        </div>
        
        <div class="bubble bubble-bottom-right">
            右下角对话气泡<br>适合右侧角色
        </div>
        
        <div class="bubble bubble-thought">
            思考气泡<br>用于内心独白
        </div>
        
        <div class="bubble bubble-shout">
            喊叫气泡！<br>表达强烈情感！
        </div>
    </div>
    
    <script>
        function updateBubble() {
            const bubble = document.getElementById('previewBubble');
            const type = document.getElementById('bubbleType').value;
            const position = document.getElementById('tailPosition').value;
            const text = document.getElementById('bubbleText').value;
            
            // 清除所有样式类
            bubble.className = 'bubble';
            
            // 添加新样式
            if (type === 'thought') {
                bubble.classList.add('bubble-thought');
            } else if (type === 'shout') {
                bubble.classList.add('bubble-shout');
            } else {
                bubble.classList.add('bubble-' + position);
            }
            
            // 更新文字
            bubble.textContent = text;
        }
        
        // 绑定事件
        document.getElementById('bubbleType').addEventListener('change', updateBubble);
        document.getElementById('tailPosition').addEventListener('change', updateBubble);
        document.getElementById('bubbleText').addEventListener('input', updateBubble);
    </script>
</body>
</html>
