/**
 * 视口管理器 - 负责视口尺寸计算和管理
 */
class ViewportManager {
    constructor() {
        this.canvasContainer = null;
        this.resizeObserver = null;
        this.callbacks = {
            onResize: []
        };

        // 性能优化：防抖处理
        this.resizeDebounceTimer = null;
        this.resizeDebounceDelay = 150; // 150ms防抖

        // 延迟初始化，确保DOM已加载
        this.initializeWhenReady();
    }
    
    /**
     * 等待DOM准备好后初始化
     */
    initializeWhenReady() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.init();
            });
        } else {
            // DOM已经加载完成，直接初始化
            this.init();
        }
    }

    /**
     * 初始化视口管理器
     */
    init() {
        try {
            this.canvasContainer = document.getElementById('canvas-container');

            if (!this.canvasContainer) {
                throw new Error('未找到canvas-container元素，请检查HTML结构');
            }

            // 设置ResizeObserver监听容器尺寸变化
            if (window.ResizeObserver) {
                this.resizeObserver = new ResizeObserver((entries) => {
                    this.handleResize(entries);
                });

                this.resizeObserver.observe(this.canvasContainer);
            } else {
                // 降级方案：使用window resize事件
                window.addEventListener('resize', () => {
                    this.handleResize();
                });
            }

            console.log('视口管理器初始化完成');
        } catch (error) {
            console.error('视口管理器初始化失败:', error);
            throw error; // 重新抛出错误，让上层处理
        }
    }
    
    /**
     * 获取视口尺寸
     */
    getViewportDimensions() {
        if (!this.canvasContainer) {
            return { width: 800, height: 600 };
        }
        
        const rect = this.canvasContainer.getBoundingClientRect();
        const computedStyle = window.getComputedStyle(this.canvasContainer);
        
        // 计算可用的视口尺寸（减去内边距）
        const paddingLeft = parseFloat(computedStyle.paddingLeft) || 0;
        const paddingRight = parseFloat(computedStyle.paddingRight) || 0;
        const paddingTop = parseFloat(computedStyle.paddingTop) || 0;
        const paddingBottom = parseFloat(computedStyle.paddingBottom) || 0;
        
        const availableWidth = rect.width - paddingLeft - paddingRight;
        const availableHeight = rect.height - paddingTop - paddingBottom;
        
        return {
            width: Math.max(availableWidth, 200),  // 最小宽度200px
            height: Math.max(availableHeight, 150), // 最小高度150px
            paddingLeft,
            paddingRight,
            paddingTop,
            paddingBottom
        };
    }
    
    /**
     * 获取可编辑区域尺寸（减去边距）
     */
    getEditableAreaDimensions(padding = 20) {
        const viewport = this.getViewportDimensions();
        
        return {
            width: Math.max(viewport.width - (padding * 2), 100),
            height: Math.max(viewport.height - (padding * 2), 100)
        };
    }
    
    /**
     * 处理尺寸变化（带防抖优化）
     */
    handleResize(entries) {
        // 清除之前的定时器
        if (this.resizeDebounceTimer) {
            clearTimeout(this.resizeDebounceTimer);
        }
        
        // 设置防抖定时器
        this.resizeDebounceTimer = setTimeout(() => {
            this.performResize();
        }, this.resizeDebounceDelay);
    }
    
    /**
     * 执行实际的尺寸变化处理
     */
    performResize() {
        try {
            const viewport = this.getViewportDimensions();
            
            // 触发所有注册的回调函数
            this.callbacks.onResize.forEach(callback => {
                try {
                    callback(viewport);
                } catch (error) {
                    console.error('视口尺寸变化回调执行失败:', error);
                }
            });
            
            console.log('视口尺寸已更新:', viewport);
        } catch (error) {
            console.error('处理视口尺寸变化失败:', error);
        }
    }
    
    /**
     * 注册尺寸变化回调
     */
    onResize(callback) {
        if (typeof callback === 'function') {
            this.callbacks.onResize.push(callback);
        }
    }
    
    /**
     * 移除尺寸变化回调
     */
    offResize(callback) {
        const index = this.callbacks.onResize.indexOf(callback);
        if (index > -1) {
            this.callbacks.onResize.splice(index, 1);
        }
    }
    
    /**
     * 更新画布显示方式
     */
    updateCanvasDisplay(canvasElement, imageDimensions, scaledDimensions) {
        if (!canvasElement || !this.canvasContainer) {
            return;
        }
        
        const viewport = this.getViewportDimensions();
        
        // 判断是否需要滚动条
        const needsScrollX = scaledDimensions.width > viewport.width;
        const needsScrollY = scaledDimensions.height > viewport.height;
        
        if (needsScrollX || needsScrollY) {
            // 大图片：允许滚动
            this.canvasContainer.style.overflow = 'auto';
            canvasElement.style.display = 'block';
            canvasElement.style.margin = '20px auto';
        } else {
            // 小图片：居中显示
            this.canvasContainer.style.overflow = 'hidden';
            canvasElement.style.display = 'block';
            canvasElement.style.margin = 'auto';
        }
        
        // 更新滚动位置（居中显示）
        this.centerScrollPosition(scaledDimensions);
    }
    
    /**
     * 居中滚动位置
     */
    centerScrollPosition(imageDimensions) {
        if (!this.canvasContainer) {
            return;
        }
        
        const viewport = this.getViewportDimensions();
        
        // 计算居中的滚动位置
        const scrollLeft = Math.max(0, (imageDimensions.width - viewport.width) / 2);
        const scrollTop = Math.max(0, (imageDimensions.height - viewport.height) / 2);
        
        this.canvasContainer.scrollLeft = scrollLeft;
        this.canvasContainer.scrollTop = scrollTop;
    }
    
    /**
     * 检查图片是否适合视口
     */
    isImageFitInViewport(imageWidth, imageHeight, padding = 20) {
        const editableArea = this.getEditableAreaDimensions(padding);
        
        return imageWidth <= editableArea.width && imageHeight <= editableArea.height;
    }
    
    /**
     * 获取画布容器元素
     */
    getCanvasContainer() {
        return this.canvasContainer;
    }
    
    /**
     * 销毁视口管理器
     */
    destroy() {
        // 清理防抖定时器
        if (this.resizeDebounceTimer) {
            clearTimeout(this.resizeDebounceTimer);
            this.resizeDebounceTimer = null;
        }
        
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
            this.resizeObserver = null;
        }
        
        this.callbacks.onResize = [];
        this.canvasContainer = null;
        
        console.log('视口管理器已销毁');
    }
}

// 导出视口管理器
window.ViewportManager = ViewportManager;