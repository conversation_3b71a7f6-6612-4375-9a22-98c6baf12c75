<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片缩放和居中功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-viewport {
            width: 800px;
            height: 600px;
            border: 2px solid #007bff;
            background: #f8f9fa;
            position: relative;
            margin: 20px 0;
        }
        
        .test-image {
            background: #28a745;
            position: absolute;
            border: 1px solid #1e7e34;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .results {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>图片缩放和居中功能测试</h1>
    
    <div class="test-container">
        <h2>测试场景</h2>
        <button onclick="testSmallImage()">测试小图片 (400x300)</button>
        <button onclick="testLargeImage()">测试大图片 (2000x1500)</button>
        <button onclick="testWideImage()">测试超宽图片 (3000x500)</button>
        <button onclick="testTallImage()">测试超高图片 (500x2000)</button>
        <button onclick="resetTest()">重置</button>
        
        <div class="test-viewport" id="testViewport">
            <div style="position: absolute; top: 10px; left: 10px; font-size: 12px; background: rgba(255,255,255,0.8); padding: 5px; border-radius: 3px;">
                视口: 800x600
            </div>
        </div>
        
        <div class="results" id="results">
            等待测试...
        </div>
    </div>

    <!-- 加载我们的脚本 -->
    <script src="src/js/viewport-manager.js"></script>
    <script src="src/js/image-scaler.js"></script>
    
    <script>
        // 创建测试实例
        const imageScaler = new ImageScaler({
            autoScale: true,
            preventUpscaling: true,
            centerImage: true,
            maxScale: 1.0,
            minScale: 0.1,
            padding: 20,
            maintainAspectRatio: true
        });
        
        function testImage(width, height, name) {
            const viewport = document.getElementById('testViewport');
            const results = document.getElementById('results');
            
            // 清除之前的测试结果
            const existingImage = viewport.querySelector('.test-image');
            if (existingImage) {
                existingImage.remove();
            }
            
            // 计算变换
            const transform = imageScaler.calculateImageTransform(
                width, height, 
                800 - 40, 600 - 40  // 减去padding
            );
            
            // 创建测试图片元素
            const imageElement = document.createElement('div');
            imageElement.className = 'test-image';
            imageElement.style.width = transform.scaledSize.width + 'px';
            imageElement.style.height = transform.scaledSize.height + 'px';
            imageElement.style.left = (transform.position.x + 20) + 'px';
            imageElement.style.top = (transform.position.y + 20) + 'px';
            imageElement.textContent = `${Math.round(transform.scaledSize.width)}x${Math.round(transform.scaledSize.height)}`;
            
            viewport.appendChild(imageElement);
            
            // 显示结果
            const statusReport = imageScaler.generateStatusReport(transform);
            results.innerHTML = `
                <strong>测试: ${name}</strong><br>
                ${statusReport}<br>
                缩放比例: ${(transform.scale * 100).toFixed(1)}%<br>
                位置: (${transform.position.x.toFixed(1)}, ${transform.position.y.toFixed(1)})<br>
                是否缩放: ${transform.isScaled ? '是' : '否'}<br>
                适合容器: ${transform.isFitToContainer ? '是' : '否'}<br>
                需要滚动条: ${transform.needsScrollbars ? '是' : '否'}
            `;
        }
        
        function testSmallImage() {
            testImage(400, 300, '小图片');
        }
        
        function testLargeImage() {
            testImage(2000, 1500, '大图片');
        }
        
        function testWideImage() {
            testImage(3000, 500, '超宽图片');
        }
        
        function testTallImage() {
            testImage(500, 2000, '超高图片');
        }
        
        function resetTest() {
            const viewport = document.getElementById('testViewport');
            const results = document.getElementById('results');
            
            const existingImage = viewport.querySelector('.test-image');
            if (existingImage) {
                existingImage.remove();
            }
            
            results.textContent = '等待测试...';
        }
        
        // 自动运行测试
        setTimeout(() => {
            testSmallImage();
        }, 100);
    </script>
</body>
</html>