# 漫画对话气泡编辑器

一个基于Electron的桌面应用，用于为图片添加漫画风格的对话气泡。

## 功能特性

- 🖼️ **图片导入**: 支持多种图片格式（PNG, JPG, GIF, BMP, WebP）
- 📏 **智能缩放**: 图片自动按编辑窗口比例缩放（仅缩小不放大）
- 🎯 **居中显示**: 图片在编辑窗口中自动居中
- 💬 **对话气泡**: 自动根据文字内容调整气泡大小
- 🎨 **样式自定义**: 可调整字体大小、气泡颜色、边框样式
- 🖱️ **拖拽操作**: 自由拖动气泡到任意位置
- ✏️ **文字编辑**: 双击气泡可编辑文字内容
- 📱 **响应式界面**: 自适应不同窗口尺寸
- 💾 **图片导出**: 将编辑结果导出为PNG或JPG格式

## 安装和运行

### 环境要求
- Node.js 16.0 或更高版本
- npm 或 yarn

### 安装步骤

1. 克隆或下载项目到本地
2. 在项目目录中安装依赖：
```bash
npm install
```

3. 启动应用：
```bash
npm start
```

### 开发模式
```bash
npm run dev
```

### 打包应用
```bash
npm run build
```

## 使用说明

### 🚀 快速开始

1. **启动应用**
   ```bash
   npm run dev
   ```

2. **导入背景图片**
   - 点击"导入图片"按钮或拖拽图片到编辑区域
   - 选择一张图片作为背景
   - 图片会自动智能缩放并居中显示
   - 支持大小为几MB的高分辨率图片

3. **添加对话气泡**
   - 在文本输入框中输入对话内容
   - 点击"添加气泡"按钮或按回车键
   - 气泡会出现在画布中央

4. **调整气泡位置**
   - 点击选中气泡
   - 拖动到合适的位置

5. **编辑气泡内容**
   - 双击气泡可以编辑文字内容

6. **自定义样式**
   - 使用右侧面板调整字体大小
   - 选择气泡颜色
   - 更改边框样式

7. **导出图片**
   - 点击"导出图片"按钮
   - 选择保存位置和格式

### ✅ 当前状态

- ✅ **文件导入功能** - 完全正常工作
- ✅ **图片智能缩放** - 自动适应窗口大小并居中显示  
- ✅ **Electron架构** - 主进程和渲染进程通信正常
- ✅ **用户界面** - 现代化响应式设计
- 🔄 **画布功能** - 正在测试中
- 🔄 **对话气泡** - 正在测试中
- 🔄 **导出功能** - 正在测试中

### 快捷键

- `Delete`: 删除选中的气泡
- `Enter`: 添加气泡（在文本输入框中）
- `Ctrl+点击`: 多选气泡（计划功能）

### 支持的文件格式

**导入格式**: PNG, JPG, JPEG, GIF, BMP, WebP
**导出格式**: PNG, JPG

## 技术架构

- **前端框架**: Electron + HTML5 + CSS3 + JavaScript
- **画布库**: Fabric.js
- **构建工具**: electron-builder

### 项目结构
```
漫画风格的对话气泡/
├── main.js                    # Electron主进程
├── preload.js                 # 预加载脚本
├── package.json               # 项目配置
├── src/
│   ├── index.html             # 主界面
│   ├── css/
│   │   └── style.css          # 样式文件
│   ├── js/
│   │   ├── app.js             # 主应用逻辑
│   │   ├── canvas-manager.js  # 画布管理
│   │   ├── viewport-manager.js # 视口管理
│   │   ├── image-scaler.js    # 图片缩放
│   │   ├── config-manager.js  # 配置管理
│   │   ├── speech-bubble.js   # 对话气泡类
│   │   ├── file-manager.js    # 文件操作
│   │   └── export-manager.js  # 导出功能
│   └── assets/
│       └── icons/             # 应用图标
├── test-image-scaling.html    # 缩放功能测试页面
├── IMAGE_SCALING_GUIDE.md     # 图片缩放功能说明
└── dist/                      # 打包输出
```

## 开发说明

### 核心模块

1. **CanvasManager**: 管理Fabric.js画布和背景图片，支持智能缩放
2. **ViewportManager**: 管理视口尺寸和响应式布局
3. **ImageScaler**: 负责图片缩放计算和居中定位
4. **SpeechBubble**: 创建和管理对话气泡对象
5. **ConfigManager**: 管理应用配置和用户设置
6. **FileManager**: 处理图片文件的导入
7. **ExportManager**: 处理画布内容的导出
8. **App**: 主应用控制器，协调各模块工作

### 自定义功能扩展

要添加新的气泡样式或功能，可以：

1. 修改 `SpeechBubble` 类添加新的样式选项
2. 在 `CanvasManager` 中添加新的交互功能
3. 更新UI界面添加相应的控制元素

## 故障排除

### 常见问题

1. **应用启动时出现GPU错误**
   - 这是Electron的常见警告，不影响功能使用

2. **图片无法导入**
   - 检查图片格式是否支持
   - 确保图片文件没有损坏
   - 尝试使用较小的图片文件

3. **导出功能不工作**
   - 确保已经导入了背景图片
   - 检查是否有足够的磁盘空间

4. **气泡显示异常**
   - 刷新应用或重新导入图片
   - 检查文字内容是否包含特殊字符

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 更新日志

### v1.1.0 (最新)
- ✨ 新增图片智能缩放和居中功能
- ✨ 添加响应式视口管理
- ✨ 支持大图片自动适配窗口
- ✨ 优化用户界面体验
- ✨ 添加性能优化和错误处理
- ✨ 完善配置管理系统

### v1.0.0
- 基础功能实现
- 图片导入和导出
- 对话气泡创建和编辑
- 样式自定义
- 拖拽交互
