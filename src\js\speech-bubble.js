/**
 * 对话气泡类 - 创建和管理漫画风格的对话气泡
 * 使用SVG路径创建完整的一体化气泡
 */
class SpeechBubble {
    constructor(text, options = {}) {
        this.text = text;
        this.options = {
            fontSize: 16,
            fontFamily: '沐瑶软笔手写体', // 默认使用沐瑶软笔手写体
            fill: '#ffffff',
            stroke: '#000000',
            strokeWidth: 2.5,
            padding: 12, // 减少内边距
            borderRadius: 20,
            tailSize: 16,
            tailPosition: 'bottom-left', // bottom-left, bottom-right, top-left, top-right, left, right
            maxWidth: 200, // 减少最大宽度，促进换行
            minWidth: 80,  // 添加最小宽度
            minHeight: 40, // 添加最小高度
            lineHeight: 1.2, // 减少行高
            bubbleType: 'speech', // speech, thought, shout
            shadowEnabled: true,
            shadowColor: 'rgba(0,0,0,0.2)',
            shadowOffset: 3,
            autoSize: true, // 自动调整大小
            opacity: 1.0, // 添加透明度控制
            ...options
        };

        this.textObject = null;
        this.bubbleShape = null;
        this.shadowShape = null;
        this.group = null;
    }
    
    /**
     * 计算文字尺寸
     */
    calculateTextDimensions() {
        // 创建临时文字对象来测量尺寸
        const tempText = new fabric.Text(this.text, {
            fontSize: this.options.fontSize,
            fontFamily: this.options.fontFamily,
            lineHeight: this.options.lineHeight
        });
        
        // 智能文字换行处理
        let lines = this.smartTextWrap(this.text, tempText, this.options.maxWidth);

        // 如果只有一行但太长，强制换行
        if (lines.length === 1 && tempText.width > this.options.maxWidth) {
            lines = this.forceWrapText(this.text, tempText, this.options.maxWidth);
        }
        
        // 计算最终文字对象
        const finalText = lines.join('\n');
        tempText.set('text', finalText);
        
        // 计算更紧凑的尺寸
        const textWidth = Math.min(tempText.width, this.options.maxWidth);
        const textHeight = tempText.height;

        // 根据文字内容动态调整气泡尺寸
        const bubbleWidth = Math.max(
            Math.min(textWidth + this.options.padding * 2, this.options.maxWidth),
            this.options.minWidth
        );
        const bubbleHeight = Math.max(
            textHeight + this.options.padding * 1.5, // 减少垂直内边距
            this.options.minHeight
        );

        return {
            width: bubbleWidth,
            height: bubbleHeight,
            textWidth: textWidth,
            textHeight: textHeight,
            text: finalText
        };
    }
    
    /**
     * 创建文字对象
     */
    createTextObject() {
        const textDimensions = this.calculateTextDimensions();
        
        this.textObject = new fabric.Text(textDimensions.text, {
            fontSize: this.options.fontSize,
            fontFamily: this.options.fontFamily,
            fill: '#000000',
            textAlign: 'center',
            originX: 'center',
            originY: 'center',
            lineHeight: this.options.lineHeight,
            selectable: false
        });
        
        return textDimensions;
    }
    
    /**
     * 创建完整的气泡形状（包含尾巴）
     */
    createBubbleShape(bubbleWidth, bubbleHeight) {
        // 直接使用传入的气泡尺寸，不再重新计算

        // 根据气泡类型创建不同的SVG路径
        let bubblePath;

        if (this.options.bubbleType === 'thought') {
            // 思考气泡 - 云朵形状 + 小圆点
            bubblePath = this.createThoughtBubblePath(bubbleWidth, bubbleHeight);
        } else if (this.options.bubbleType === 'shout') {
            // 喊叫气泡 - 锯齿边框 + 尖锐尾巴
            bubblePath = this.createShoutBubblePath(bubbleWidth, bubbleHeight);
        } else {
            // 普通对话气泡 - 圆角矩形 + 三角尾巴
            bubblePath = this.createSpeechBubblePath(bubbleWidth, bubbleHeight);
        }

        // 创建SVG路径对象
        this.bubbleShape = new fabric.Path(bubblePath, {
            fill: this.options.fill,
            stroke: this.options.stroke,
            strokeWidth: this.options.strokeWidth,
            originX: 'center',
            originY: 'center',
            selectable: false,
            opacity: this.options.opacity // 添加透明度
        });

        // 设置边框样式
        if (this.options.borderStyle === 'dashed') {
            this.bubbleShape.set('strokeDashArray', [8, 4]);
        } else if (this.options.borderStyle === 'dotted') {
            this.bubbleShape.set('strokeDashArray', [3, 3]);
        }

        // 创建阴影
        if (this.options.shadowEnabled) {
            this.shadowShape = new fabric.Path(bubblePath, {
                fill: this.options.shadowColor,
                stroke: 'transparent',
                left: this.options.shadowOffset,
                top: this.options.shadowOffset,
                originX: 'center',
                originY: 'center',
                selectable: false
            });
        }

        return { width: bubbleWidth, height: bubbleHeight };
    }

    /**
     * 创建椭圆形对话气泡路径（椭圆 + 三角尾巴）
     */
    createSpeechBubblePath(width, height) {
        const w = width / 2;
        const h = height / 2;
        const tailSize = this.options.tailSize;

        // 创建椭圆形路径
        let path = `M ${-w} ${0}`;

        // 椭圆的上半部分
        path += ` C ${-w} ${-h * 0.55} ${-w * 0.55} ${-h} ${0} ${-h}`;
        path += ` C ${w * 0.55} ${-h} ${w} ${-h * 0.55} ${w} ${0}`;

        // 椭圆的下半部分，根据尾巴位置进行调整
        switch (this.options.tailPosition) {
            case 'bottom-left':
                // 右下弧线
                path += ` C ${w} ${h * 0.55} ${w * 0.55} ${h} ${w * 0.2} ${h}`;
                // 添加三角尾巴
                path += ` L ${-w * 0.2} ${h}`;
                path += ` L ${-w * 0.3} ${h + tailSize}`;
                path += ` L ${-w * 0.4} ${h}`;
                // 左下弧线
                path += ` C ${-w * 0.55} ${h} ${-w} ${h * 0.55} ${-w} ${0}`;
                break;

            case 'bottom-right':
                // 右下弧线，添加尾巴
                path += ` C ${w} ${h * 0.55} ${w * 0.55} ${h} ${w * 0.4} ${h}`;
                path += ` L ${w * 0.3} ${h + tailSize}`;
                path += ` L ${w * 0.2} ${h}`;
                // 左下弧线
                path += ` C ${-w * 0.2} ${h} ${-w * 0.55} ${h} ${-w} ${h * 0.55}`;
                path += ` C ${-w} ${h * 0.55} ${-w} ${0}`;
                break;

            case 'left':
                // 右下弧线
                path += ` C ${w} ${h * 0.55} ${w * 0.55} ${h} ${0} ${h}`;
                // 左下弧线，添加尾巴
                path += ` C ${-w * 0.55} ${h} ${-w} ${h * 0.55} ${-w} ${h * 0.2}`;
                path += ` L ${-w - tailSize} ${0}`;
                path += ` L ${-w} ${-h * 0.2}`;
                path += ` C ${-w} ${-h * 0.55} ${-w} ${0}`;
                break;

            case 'right':
                // 右下弧线，添加尾巴
                path += ` C ${w} ${h * 0.2} ${w} ${h * 0.55}`;
                path += ` L ${w + tailSize} ${0}`;
                path += ` L ${w} ${-h * 0.2}`;
                path += ` C ${w} ${h * 0.55} ${w * 0.55} ${h} ${0} ${h}`;
                // 左下弧线
                path += ` C ${-w * 0.55} ${h} ${-w} ${h * 0.55} ${-w} ${0}`;
                break;

            default:
                // 默认底部左侧
                path += ` C ${w} ${h * 0.55} ${w * 0.55} ${h} ${w * 0.2} ${h}`;
                path += ` L ${-w * 0.2} ${h}`;
                path += ` L ${-w * 0.3} ${h + tailSize}`;
                path += ` L ${-w * 0.4} ${h}`;
                path += ` C ${-w * 0.55} ${h} ${-w} ${h * 0.55} ${-w} ${0}`;
        }

        path += ' Z';
        return path;
    }

    /**
     * 创建思考气泡路径（椭圆云朵形状 + 小圆点）
     */
    createThoughtBubblePath(width, height) {
        const w = width / 2;
        const h = height / 2;

        // 创建基础椭圆
        let path = `M ${-w} ${0}`;

        // 椭圆的上半部分，添加云朵凸起效果
        path += ` C ${-w} ${-h * 0.7} ${-w * 0.7} ${-h * 1.1} ${-w * 0.3} ${-h * 1.1}`;
        path += ` C ${-w * 0.1} ${-h * 1.2} ${w * 0.1} ${-h * 1.2} ${w * 0.3} ${-h * 1.1}`;
        path += ` C ${w * 0.7} ${-h * 1.1} ${w} ${-h * 0.7} ${w} ${0}`;

        // 椭圆的下半部分，也添加一些云朵效果
        path += ` C ${w} ${h * 0.7} ${w * 0.7} ${h * 1.1} ${w * 0.3} ${h * 1.1}`;
        path += ` C ${w * 0.1} ${h * 1.2} ${-w * 0.1} ${h * 1.2} ${-w * 0.3} ${h * 1.1}`;
        path += ` C ${-w * 0.7} ${h * 1.1} ${-w} ${h * 0.7} ${-w} ${0}`;

        path += ' Z';

        // 添加思考圆点（从大到小）
        const dot1X = -w * 0.3;
        const dot1Y = h + 20;
        const dot2X = -w * 0.4;
        const dot2Y = h + 35;
        const dot3X = -w * 0.5;
        const dot3Y = h + 45;

        // 大圆点
        path += ` M ${dot1X - 6} ${dot1Y} A 6 6 0 1 1 ${dot1X + 6} ${dot1Y} A 6 6 0 1 1 ${dot1X - 6} ${dot1Y}`;
        // 中圆点
        path += ` M ${dot2X - 4} ${dot2Y} A 4 4 0 1 1 ${dot2X + 4} ${dot2Y} A 4 4 0 1 1 ${dot2X - 4} ${dot2Y}`;
        // 小圆点
        path += ` M ${dot3X - 2} ${dot3Y} A 2 2 0 1 1 ${dot3X + 2} ${dot3Y} A 2 2 0 1 1 ${dot3X - 2} ${dot3Y}`;

        return path;
    }

    /**
     * 创建喊叫气泡路径（锯齿边框 + 尖锐尾巴）
     */
    createShoutBubblePath(width, height) {
        const w = width / 2;
        const h = height / 2;
        const zigzag = 6;
        const tailSize = this.options.tailSize;

        let path = `M ${-w} ${-h + zigzag}`;

        // 顶边锯齿
        for (let x = -w; x < w; x += zigzag * 2) {
            const nextX = Math.min(x + zigzag * 2, w);
            path += ` L ${x + zigzag} ${-h - zigzag/2}`;
            path += ` L ${nextX} ${-h + zigzag}`;
        }

        // 右边锯齿
        for (let y = -h + zigzag; y < h; y += zigzag * 2) {
            const nextY = Math.min(y + zigzag * 2, h);
            path += ` L ${w + zigzag/2} ${y + zigzag}`;
            path += ` L ${w - zigzag} ${nextY}`;
        }

        // 底边（包含尖锐尾巴）
        if (this.options.tailPosition === 'bottom-left') {
            path += ` L ${-w/4 + tailSize} ${h - zigzag}`;
            path += ` L ${-w/4} ${h + tailSize * 1.5}`;
            path += ` L ${-w/4 - tailSize} ${h - zigzag}`;
        }

        for (let x = w; x > -w; x -= zigzag * 2) {
            const nextX = Math.max(x - zigzag * 2, -w);
            if (!(this.options.tailPosition === 'bottom-left' && x > -w/4 - tailSize && x < -w/4 + tailSize)) {
                path += ` L ${x - zigzag} ${h + zigzag/2}`;
                path += ` L ${nextX} ${h - zigzag}`;
            }
        }

        // 左边锯齿
        for (let y = h - zigzag; y > -h; y -= zigzag * 2) {
            const nextY = Math.max(y - zigzag * 2, -h);
            path += ` L ${-w - zigzag/2} ${y - zigzag}`;
            path += ` L ${-w + zigzag} ${nextY}`;
        }

        path += ' Z';
        return path;
    }
    

    
    /**
     * 创建完整的Fabric.js对象组
     */
    createFabricObject() {
        // 创建文字对象并获取尺寸
        const textDimensions = this.createTextObject();

        // 创建完整的气泡形状（包含尾巴）
        this.createBubbleShape(textDimensions.width, textDimensions.height);

        // 创建对象组
        const objects = [];

        // 添加阴影（如果启用）
        if (this.shadowShape) {
            objects.push(this.shadowShape);
        }

        // 添加主要气泡形状
        objects.push(this.bubbleShape);

        // 添加文字（最后添加，确保在最上层）
        objects.push(this.textObject);

        this.group = new fabric.Group(objects, {
            selectable: true,
            hasControls: true,
            hasBorders: true,
            // 启用缩放功能
            lockScalingX: false,
            lockScalingY: false,
            // 可以选择是否允许旋转
            lockRotation: false,
            // 美化控制点样式
            cornerStyle: 'circle',
            cornerSize: 12,
            transparentCorners: false,
            cornerColor: '#4A90E2',
            cornerStrokeColor: '#ffffff',
            borderColor: '#4A90E2',
            borderScaleFactor: 2,
            // 保持宽高比缩放（可选）
            lockUniScaling: false
        });

        // 保存气泡数据到组对象
        this.group.speechBubbleData = {
            text: this.text,
            options: this.options
        };

        return this.group;
    }
    
    /**
     * 更新气泡文字
     */
    updateText(newText) {
        this.text = newText;
        
        if (this.textObject) {
            const textDimensions = this.calculateTextDimensions();
            this.textObject.set('text', textDimensions.text);
            
            // 重新计算气泡尺寸
            const bubbleWidth = textDimensions.width + this.options.padding * 2;
            const bubbleHeight = textDimensions.height + this.options.padding * 2;
            
            this.bubbleShape.set({
                width: bubbleWidth,
                height: bubbleHeight
            });
            
            // 更新尾巴位置
            this.updateTailPosition(bubbleWidth, bubbleHeight);
            
            if (this.group) {
                this.group.speechBubbleData.text = newText;
            }
        }
    }
    
    /**
     * 更新尾巴位置
     */
    updateTailPosition(bubbleWidth, bubbleHeight) {
        if (!this.tailShape) return;
        
        let tailLeft = 0;
        let tailTop = 0;
        
        switch (this.options.tailPosition) {
            case 'bottom':
                tailLeft = 0;
                tailTop = bubbleHeight/2;
                break;
            case 'top':
                tailLeft = 0;
                tailTop = -bubbleHeight/2;
                break;
            case 'left':
                tailLeft = -bubbleWidth/2;
                tailTop = 0;
                break;
            case 'right':
                tailLeft = bubbleWidth/2;
                tailTop = 0;
                break;
        }
        
        this.tailShape.set({
            left: tailLeft,
            top: tailTop
        });
    }
    
    /**
     * 更新样式选项
     */
    updateOptions(newOptions) {
        this.options = { ...this.options, ...newOptions };
        
        if (this.bubbleShape) {
            this.bubbleShape.set({
                fill: this.options.fill,
                stroke: this.options.stroke,
                strokeWidth: this.options.strokeWidth
            });
        }
        
        if (this.tailShape) {
            this.tailShape.set({
                fill: this.options.fill,
                stroke: this.options.stroke,
                strokeWidth: this.options.strokeWidth
            });
        }
        
        if (this.textObject) {
            this.textObject.set({
                fontSize: this.options.fontSize,
                fontFamily: this.options.fontFamily
            });
        }
        
        if (this.group) {
            this.group.speechBubbleData.options = this.options;
        }
    }

    /**
     * 自动调整气泡大小以适应文字
     */
    autoResize() {
        if (!this.textObject || !this.group) return;

        const textDimensions = this.calculateTextDimensions();

        // 更新文字
        this.textObject.set('text', textDimensions.text);

        // 重新创建气泡形状
        this.createBubbleShape(textDimensions.width, textDimensions.height);

        // 刷新画布
        if (this.group.canvas) {
            this.group.canvas.renderAll();
        }
    }

    /**
     * 设置气泡缩放
     */
    setScale(scaleX, scaleY = null) {
        if (!this.group) return;

        if (scaleY === null) scaleY = scaleX;

        this.group.set({
            scaleX: scaleX,
            scaleY: scaleY
        });

        if (this.group.canvas) {
            this.group.canvas.renderAll();
        }
    }

    /**
     * 获取当前缩放比例
     */
    getScale() {
        if (!this.group) return { scaleX: 1, scaleY: 1 };

        return {
            scaleX: this.group.scaleX || 1,
            scaleY: this.group.scaleY || 1
        };
    }

    /**
     * 智能文字换行
     */
    smartTextWrap(text, tempTextObj, maxWidth) {
        // 定义分割符号的优先级
        const breakPoints = [
            { chars: ['。', '！', '？', '.', '!', '?'], priority: 1 }, // 句号优先级最高
            { chars: ['，', '、', ',', ';', '；'], priority: 2 },      // 逗号次之
            { chars: [' '], priority: 3 },                           // 空格
            { chars: ['哎', '啊', '呀', '哦', '嗯', '唉'], priority: 4 } // 语气词
        ];

        let lines = [];
        let remainingText = text;

        while (remainingText.length > 0) {
            // 测试当前剩余文字是否超出宽度
            tempTextObj.set('text', remainingText);
            if (tempTextObj.width <= maxWidth) {
                // 如果不超出，直接添加到行中
                lines.push(remainingText);
                break;
            }

            // 找到最佳断点
            let bestBreakPoint = this.findBestBreakPoint(remainingText, tempTextObj, maxWidth, breakPoints);

            if (bestBreakPoint > 0) {
                lines.push(remainingText.substring(0, bestBreakPoint).trim());
                remainingText = remainingText.substring(bestBreakPoint).trim();
            } else {
                // 如果找不到合适的断点，强制按字符分割
                let charBreakPoint = this.findCharBreakPoint(remainingText, tempTextObj, maxWidth);
                if (charBreakPoint > 0) {
                    lines.push(remainingText.substring(0, charBreakPoint));
                    remainingText = remainingText.substring(charBreakPoint);
                } else {
                    // 如果连一个字符都放不下，直接添加
                    lines.push(remainingText);
                    break;
                }
            }
        }

        return lines;
    }

    /**
     * 找到最佳断点
     */
    findBestBreakPoint(text, tempTextObj, maxWidth, breakPoints) {
        let bestPoint = -1;
        let bestPriority = 999;

        // 按优先级查找断点
        for (let bp of breakPoints) {
            if (bp.priority > bestPriority) continue;

            for (let char of bp.chars) {
                let index = 0;
                while ((index = text.indexOf(char, index)) !== -1) {
                    // 测试到这个断点的文字宽度
                    let testText = text.substring(0, index + 1);
                    tempTextObj.set('text', testText);

                    if (tempTextObj.width <= maxWidth) {
                        if (bp.priority < bestPriority || index > bestPoint) {
                            bestPoint = index + 1;
                            bestPriority = bp.priority;
                        }
                    } else {
                        break; // 超出宽度，不再继续查找这个字符
                    }
                    index++;
                }
            }
        }

        return bestPoint;
    }

    /**
     * 按字符强制分割
     */
    findCharBreakPoint(text, tempTextObj, maxWidth) {
        for (let i = 1; i <= text.length; i++) {
            tempTextObj.set('text', text.substring(0, i));
            if (tempTextObj.width > maxWidth) {
                return Math.max(1, i - 1); // 至少返回1个字符
            }
        }
        return text.length;
    }

    /**
     * 强制换行（备用方法）
     */
    forceWrapText(text, tempTextObj, maxWidth) {
        let lines = [];
        let currentLine = '';

        for (let char of text) {
            const testLine = currentLine + char;
            tempTextObj.set('text', testLine);

            if (tempTextObj.width > maxWidth && currentLine) {
                lines.push(currentLine);
                currentLine = char;
            } else {
                currentLine = testLine;
            }
        }

        if (currentLine) {
            lines.push(currentLine);
        }

        return lines;
    }
}
