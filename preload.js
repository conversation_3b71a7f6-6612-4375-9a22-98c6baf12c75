const { contextBridge, ipcRenderer } = require('electron');

console.log('Preload script loaded');

// 暴露受保护的方法，允许渲染进程使用
contextBridge.exposeInMainWorld('electronAPI', {
  // 文件操作API
  showOpenDialog: () => {
    console.log('showOpenDialog called');
    return ipcRenderer.invoke('show-open-dialog');
  },
  showSaveDialog: () => {
    console.log('showSaveDialog called');
    return ipcRenderer.invoke('show-save-dialog');
  },
  readFile: (filePath) => {
    console.log('readFile called:', filePath);
    return ipcRenderer.invoke('read-file', filePath);
  },
  writeFile: (filePath, data) => {
    console.log('writeFile called:', filePath);
    return ipcRenderer.invoke('write-file', filePath, data);
  },

  // 字体管理API
  readFontsDirectory: () => {
    console.log('readFontsDirectory called');
    return ipcRenderer.invoke('read-fonts-directory');
  },

  // 配置管理API
  loadConfig: (configFile) => {
    console.log('loadConfig called:', configFile);
    return ipcRenderer.invoke('load-config', configFile);
  },
  saveConfig: (configFile, config) => {
    console.log('saveConfig called:', configFile);
    return ipcRenderer.invoke('save-config', configFile, config);
  },

  // 应用信息
  platform: process.platform,

  // 实用工具 - 使用简单的字符串操作替代path模块
  path: {
    join: (...args) => {
      return args.join('/').replace(/\/+/g, '/');
    },
    basename: (filePath) => {
      if (!filePath) return '';
      const parts = filePath.replace(/\\/g, '/').split('/');
      return parts[parts.length - 1] || '';
    },
    extname: (filePath) => {
      if (!filePath) return '';
      const basename = filePath.replace(/\\/g, '/').split('/').pop() || '';
      const lastDot = basename.lastIndexOf('.');
      return lastDot > 0 ? basename.substring(lastDot) : '';
    }
  }
});

console.log('electronAPI exposed to main world');
