/**
 * 画布管理器 - 负责管理Fabric.js画布和背景图片
 */
class CanvasManager {
    constructor() {
        this.canvas = null;
        this.backgroundImage = null;
        this.originalImageData = null;
        this.bubbles = [];
        this.selectedBubble = null;

        // 新增：图片缩放和视口管理相关
        this.viewportManager = null;
        this.imageScaler = null;
        this.currentImageTransform = null;

        // 直接初始化，因为 App 已经确保了 Fabric.js 和 DOM 都已准备好
        this.init();
    }
    


    /**
     * 初始化画布
     */
    init() {
        try {
            // 初始化视口管理器
            this.viewportManager = new ViewportManager();

            // 初始化图片缩放器（使用默认配置）
            this.imageScaler = new ImageScaler();

            // 创建Fabric.js画布
            this.canvas = new fabric.Canvas('main-canvas', {
                backgroundColor: '#ffffff',
                selection: true,
                preserveObjectStacking: true
            });

            // 设置初始画布大小
            this.resizeCanvas(800, 600);

            // 绑定事件
            this.bindEvents();

            // 设置视口尺寸变化监听
            this.setupViewportResizeHandler();

            console.log('画布管理器初始化完成');
        } catch (error) {
            console.error('画布管理器初始化失败:', error);
            this.initFallback();
        }
    }
    
    /**
     * 降级初始化（当主初始化失败时）
     */
    initFallback() {
        try {
            console.log('启用降级模式初始化画布管理器');
            
            // 使用基本的Fabric.js初始化
            this.canvas = new fabric.Canvas('main-canvas', {
                backgroundColor: '#ffffff',
                selection: true,
                preserveObjectStacking: true
            });
            
            this.resizeCanvas(800, 600);
            this.bindEvents();
            
            // 不初始化视口管理器和图片缩放器
            this.viewportManager = null;
            this.imageScaler = null;
            
            this.updateStatus('画布初始化完成（基本模式）');
            
        } catch (fallbackError) {
            console.error('降级初始化也失败:', fallbackError);
            this.updateStatus('画布初始化失败，请刷新页面');
        }
    }
    
    /**
     * 绑定画布事件
     */
    bindEvents() {
        // 对象选择事件
        this.canvas.on('selection:created', (e) => {
            this.onObjectSelected(e.selected[0]);
        });
        
        this.canvas.on('selection:updated', (e) => {
            this.onObjectSelected(e.selected[0]);
        });
        
        this.canvas.on('selection:cleared', () => {
            this.onObjectDeselected();
        });
        
        // 对象修改事件
        this.canvas.on('object:modified', (e) => {
            this.onObjectModified(e.target);
        });
        
        // 双击编辑文字
        this.canvas.on('mouse:dblclick', (e) => {
            if (e.target && e.target.type === 'group' && e.target.speechBubbleData) {
                this.editBubbleText(e.target);
            }
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Delete' && this.canvas.getActiveObject()) {
                this.deleteSelectedObject();
            }
        });

        // 自动调整大小按钮事件
        const autoResizeBtn = document.getElementById('auto-resize-btn');
        if (autoResizeBtn) {
            autoResizeBtn.addEventListener('click', () => {
                this.autoResizeSelectedBubble();
            });
        }

        // 延迟设置拖拽功能，确保DOM已加载
        setTimeout(() => {
            this.setupDragAndDrop();
        }, 100);
    }
    
    /**
     * 设置视口尺寸变化处理
     */
    setupViewportResizeHandler() {
        if (this.viewportManager) {
            this.viewportManager.onResize((viewport) => {
                this.handleViewportResize(viewport);
            });
        }
    }
    
    /**
     * 处理视口尺寸变化
     */
    handleViewportResize(viewport) {
        if (this.backgroundImage && this.originalImageData) {
            // 重新计算图片变换
            this.recalculateImageTransform();
        }
    }
    
    /**
     * 设置背景图片（支持智能缩放和居中）
     */
    setBackgroundImage(imageUrl, callback, cropPixels = 0) {
        try {
            fabric.Image.fromURL(imageUrl, (img) => {
                try {
                    // 检查图片是否加载成功
                    if (!img || !img.width || !img.height) {
                        throw new Error('图片加载失败或格式不正确');
                    }

                    // 如果需要裁切，先进行裁切处理
                    if (cropPixels > 0) {
                        img = this.cropImage(img, cropPixels);
                    }

                    // 保存原始图片数据
                    this.originalImageData = {
                        width: img.width,
                        height: img.height,
                        src: imageUrl
                    };

                    // 尝试使用智能缩放和居中功能
                    if (this.viewportManager && this.imageScaler) {
                        this.setBackgroundImageWithScaling(img, callback);
                    } else {
                        // 降级方案：使用原始方法
                        this.setBackgroundImageFallback(img, callback);
                    }

                } catch (error) {
                    console.error('设置背景图片时出错:', error);
                    this.updateStatus('设置背景图片失败: ' + error.message);
                    if (callback) callback(error);
                }

            }, {
                crossOrigin: 'anonymous',
                // 添加错误处理
                onError: (error) => {
                    console.error('图片加载失败:', error);
                    this.updateStatus('图片加载失败，请检查文件格式');
                    if (callback) callback(new Error('图片加载失败'));
                }
            });
        } catch (error) {
            console.error('设置背景图片时出错:', error);
            this.updateStatus('设置背景图片失败: ' + error.message);
            if (callback) callback(error);
        }
    }
    
    /**
     * 使用智能缩放和居中设置背景图片
     */
    setBackgroundImageWithScaling(img, callback) {
        try {
            // 获取当前视口尺寸
            const viewport = this.viewportManager.getEditableAreaDimensions(
                this.imageScaler.getConfig().padding
            );

            // 计算图片变换
            this.currentImageTransform = this.imageScaler.calculateImageTransform(
                img.width,
                img.height,
                viewport.width,
                viewport.height
            );

            // 设置画布尺寸为缩放后的图片尺寸
            const canvasWidth = this.currentImageTransform.scaledSize.width;
            const canvasHeight = this.currentImageTransform.scaledSize.height;

            this.resizeCanvas(canvasWidth, canvasHeight);

            // 设置图片变换属性
            img.set({
                left: 0,
                top: 0,
                scaleX: this.currentImageTransform.scale,
                scaleY: this.currentImageTransform.scale
            });

            // 设置为背景图片
            this.canvas.setBackgroundImage(img, () => {
                this.canvas.renderAll();
                this.backgroundImage = img;

                // 更新画布显示方式
                this.updateCanvasDisplay();

                // 隐藏空状态
                const emptyState = document.getElementById('empty-state');
                if (emptyState) {
                    emptyState.classList.add('hidden');
                }

                // 启用导出按钮
                const exportBtn = document.getElementById('export-btn');
                if (exportBtn) {
                    exportBtn.disabled = false;
                }

                // 更新状态信息
                const statusReport = this.imageScaler.generateStatusReport(this.currentImageTransform);
                this.updateStatus(`图片已加载: ${statusReport}`);

                if (callback) callback();
            });
        } catch (error) {
            console.error('智能缩放设置背景图片失败，使用降级方案:', error);
            this.setBackgroundImageFallback(img, callback);
        }
    }
    
    /**
     * 降级方案：使用原始方法设置背景图片
     */
    setBackgroundImageFallback(img, callback) {
        try {
            console.log('使用降级方案设置背景图片');
            
            // 使用图片的原始分辨率作为画布尺寸
            const canvasWidth = img.width;
            const canvasHeight = img.height;

            // 设置画布尺寸为图片原始尺寸
            this.resizeCanvas(canvasWidth, canvasHeight);

            // 不缩放图片，保持原始分辨率
            img.set({
                left: 0,
                top: 0,
                scaleX: 1,
                scaleY: 1
            });

            // 设置为背景图片
            this.canvas.setBackgroundImage(img, () => {
                this.canvas.renderAll();
                this.backgroundImage = img;

                // 隐藏空状态
                const emptyState = document.getElementById('empty-state');
                if (emptyState) {
                    emptyState.classList.add('hidden');
                }

                // 启用导出按钮
                const exportBtn = document.getElementById('export-btn');
                if (exportBtn) {
                    exportBtn.disabled = false;
                }

                // 更新状态
                this.updateStatus(`图片已加载: ${canvasWidth}x${canvasHeight} (原始尺寸)`);

                if (callback) callback();
            });
        } catch (error) {
            console.error('降级方案也失败:', error);
            this.updateStatus('设置背景图片失败');
            if (callback) callback(error);
        }
    }
    
    /**
     * 调整画布大小
     */
    resizeCanvas(width, height) {
        // 设置画布尺寸
        this.canvas.setDimensions({
            width: width,
            height: height
        });

        // 更新画布信息，显示当前尺寸
        const canvasInfo = document.getElementById('canvas-info');
        if (canvasInfo) {
            if (this.currentImageTransform && this.currentImageTransform.isScaled) {
                const percentage = Math.round(this.currentImageTransform.scale * 100);
                canvasInfo.textContent = `画布: ${width}x${height} (${percentage}%)`;
            } else {
                canvasInfo.textContent = `画布: ${width}x${height}`;
            }
        }

        console.log(`画布尺寸已设置为: ${width}x${height}`);
    }
    
    /**
     * 更新画布显示方式
     */
    updateCanvasDisplay() {
        if (!this.currentImageTransform || !this.viewportManager) {
            return;
        }
        
        const canvasElement = this.canvas.getElement();
        
        this.viewportManager.updateCanvasDisplay(
            canvasElement,
            this.currentImageTransform.originalSize,
            this.currentImageTransform.scaledSize
        );
    }
    
    /**
     * 重新计算图片变换（在窗口尺寸变化时调用）
     */
    recalculateImageTransform() {
        if (!this.backgroundImage || !this.originalImageData) {
            return;
        }
        
        // 检查是否支持智能缩放
        if (!this.viewportManager || !this.imageScaler) {
            console.log('智能缩放未启用，跳过重新计算');
            return;
        }
        
        try {
            // 获取当前视口尺寸
            const viewport = this.viewportManager.getEditableAreaDimensions(
                this.imageScaler.getConfig().padding
            );
            
            // 重新计算图片变换
            const newTransform = this.imageScaler.calculateImageTransform(
                this.originalImageData.width,
                this.originalImageData.height,
                viewport.width,
                viewport.height
            );
            
            // 只有在缩放比例发生显著变化时才更新
            const scaleChanged = Math.abs(newTransform.scale - (this.currentImageTransform?.scale || 1)) > 0.01;
            
            if (scaleChanged) {
                this.currentImageTransform = newTransform;
                
                // 使用requestAnimationFrame优化性能
                requestAnimationFrame(() => {
                    try {
                        // 更新画布尺寸
                        this.resizeCanvas(
                            this.currentImageTransform.scaledSize.width,
                            this.currentImageTransform.scaledSize.height
                        );
                        
                        // 更新背景图片的缩放
                        this.backgroundImage.set({
                            scaleX: this.currentImageTransform.scale,
                            scaleY: this.currentImageTransform.scale
                        });
                        
                        this.canvas.renderAll();
                        
                        // 更新显示方式
                        this.updateCanvasDisplay();
                        
                        // 更新状态信息
                        const statusReport = this.imageScaler.generateStatusReport(this.currentImageTransform);
                        this.updateStatus(`视口尺寸已更新: ${statusReport}`);
                        
                        console.log('图片变换已重新计算', this.currentImageTransform);
                    } catch (renderError) {
                        console.error('重新渲染失败:', renderError);
                    }
                });
            }
            
        } catch (error) {
            console.error('重新计算图片变换失败:', error);
            // 如果重新计算失败，不做任何操作，保持当前状态
        }
    }
    
    /**
     * 获取当前图片变换信息
     */
    getCurrentImageTransform() {
        return this.currentImageTransform;
    }

    /**
     * 计算导出倍率（用于高清导出）
     * @returns {number} 导出倍率
     */
    calculateExportMultiplier() {
        try {
            // 如果没有背景图片或原始数据，返回默认倍率
            if (!this.backgroundImage || !this.originalImageData || !this.currentImageTransform) {
                console.log('无法计算导出倍率：缺少必要数据，使用默认倍率1.0');
                return 1.0;
            }

            // 获取原始图片尺寸
            const originalWidth = this.originalImageData.width;
            const originalHeight = this.originalImageData.height;

            // 获取当前画布显示尺寸
            const currentCanvasWidth = this.canvas.width;
            const currentCanvasHeight = this.canvas.height;

            // 计算倍率：原始尺寸 / 当前显示尺寸
            const widthMultiplier = originalWidth / currentCanvasWidth;
            const heightMultiplier = originalHeight / currentCanvasHeight;

            // 使用较小的倍率确保完整性，通常宽高倍率应该相同（等比缩放）
            const multiplier = Math.min(widthMultiplier, heightMultiplier);

            console.log(`导出倍率计算: 原始${originalWidth}x${originalHeight}, 显示${currentCanvasWidth}x${currentCanvasHeight}, 倍率${multiplier.toFixed(3)}`);

            // 确保倍率在合理范围内
            return Math.max(0.1, Math.min(10.0, multiplier));

        } catch (error) {
            console.error('计算导出倍率失败:', error);
            return 1.0;
        }
    }
    
    /**
     * 更新图片缩放配置
     */
    updateImageScaleConfig(config) {
        if (this.imageScaler) {
            this.imageScaler.updateConfig(config);
            
            // 如果当前有图片，重新计算变换
            if (this.backgroundImage) {
                this.recalculateImageTransform();
            }
        }
    }
    
    /**
     * 添加对话气泡
     */
    addSpeechBubble(text, options = {}) {
        if (!text.trim()) {
            this.updateStatus('请输入对话内容');
            return;
        }
        
        const bubbleOptions = {
            fontSize: options.fontSize || 16,
            fill: options.fill || '#ffffff',
            stroke: options.stroke || '#000000',
            strokeWidth: options.strokeWidth || 2,
            borderStyle: options.borderStyle || 'solid',
            ...options
        };
        
        // 创建对话气泡
        const bubble = new SpeechBubble(text, bubbleOptions);
        const bubbleGroup = bubble.createFabricObject();
        
        // 设置初始位置（画布中心）
        bubbleGroup.set({
            left: this.canvas.width / 2,
            top: this.canvas.height / 2,
            originX: 'center',
            originY: 'center'
        });
        
        // 添加到画布
        this.canvas.add(bubbleGroup);
        this.canvas.setActiveObject(bubbleGroup);
        this.canvas.renderAll();
        
        // 保存到气泡列表
        this.bubbles.push(bubbleGroup);
        
        this.updateStatus(`已添加对话气泡: "${text}"`);
        
        return bubbleGroup;
    }
    
    /**
     * 删除选中的对象
     */
    deleteSelectedObject() {
        const activeObject = this.canvas.getActiveObject();
        if (activeObject) {
            // 从气泡列表中移除
            const index = this.bubbles.indexOf(activeObject);
            if (index > -1) {
                this.bubbles.splice(index, 1);
            }
            
            this.canvas.remove(activeObject);
            this.canvas.renderAll();
            this.updateStatus('已删除选中的气泡');
        }
    }
    
    /**
     * 清空所有气泡
     */
    clearAllBubbles() {
        this.bubbles.forEach(bubble => {
            this.canvas.remove(bubble);
        });
        this.bubbles = [];
        this.canvas.renderAll();
        this.updateStatus('已清空所有气泡');
    }
    
    /**
     * 编辑气泡文字
     */
    editBubbleText(bubbleGroup) {
        const currentText = bubbleGroup.speechBubbleData.text;
        const newText = prompt('编辑对话内容:', currentText);
        
        if (newText !== null && newText.trim() !== currentText) {
            // 重新创建气泡
            const options = bubbleGroup.speechBubbleData.options;
            const bubble = new SpeechBubble(newText.trim(), options);
            const newBubbleGroup = bubble.createFabricObject();
            
            // 保持原有位置
            newBubbleGroup.set({
                left: bubbleGroup.left,
                top: bubbleGroup.top,
                originX: bubbleGroup.originX,
                originY: bubbleGroup.originY
            });
            
            // 替换旧气泡
            const index = this.bubbles.indexOf(bubbleGroup);
            if (index > -1) {
                this.bubbles[index] = newBubbleGroup;
            }
            
            this.canvas.remove(bubbleGroup);
            this.canvas.add(newBubbleGroup);
            this.canvas.setActiveObject(newBubbleGroup);
            this.canvas.renderAll();
            
            this.updateStatus(`已更新气泡文字: "${newText}"`);
        }
    }
    
    /**
     * 对象选中事件处理
     */
    onObjectSelected(obj) {
        this.selectedBubble = obj;
        document.getElementById('delete-selected-btn').disabled = false;

        // 如果选中的是气泡，启用自动调整大小按钮
        const autoResizeBtn = document.getElementById('auto-resize-btn');
        if (autoResizeBtn) {
            autoResizeBtn.disabled = !obj.speechBubbleData;
        }
    }
    
    /**
     * 对象取消选中事件处理
     */
    onObjectDeselected() {
        this.selectedBubble = null;
        document.getElementById('delete-selected-btn').disabled = true;

        // 禁用自动调整大小按钮
        const autoResizeBtn = document.getElementById('auto-resize-btn');
        if (autoResizeBtn) {
            autoResizeBtn.disabled = true;
        }
    }
    
    /**
     * 对象修改事件处理
     */
    onObjectModified(obj) {
        // 可以在这里添加修改后的处理逻辑
        console.log('对象已修改:', obj);
    }
    
    /**
     * 获取画布数据URL
     */
    getCanvasDataURL(format = 'image/png', quality = 1) {
        return this.canvas.toDataURL({
            format: format,
            quality: quality,
            multiplier: 1
        });
    }
    
    /**
     * 更新状态信息
     */
    updateStatus(message) {
        document.getElementById('status-text').textContent = message;
        console.log('状态更新:', message);
    }
    
    /**
     * 自动调整选中气泡的大小
     */
    autoResizeSelectedBubble() {
        const activeObject = this.canvas.getActiveObject();

        if (!activeObject || !activeObject.speechBubbleData) {
            this.updateStatus('请先选择一个对话气泡');
            return;
        }

        try {
            // 找到对应的气泡实例
            const bubbleData = activeObject.speechBubbleData;
            const bubble = new SpeechBubble(bubbleData.text, bubbleData.options);

            // 创建新的气泡（会自动重新计算尺寸）
            const newBubbleGroup = bubble.create();

            // 保持原来的位置和缩放
            newBubbleGroup.set({
                left: activeObject.left,
                top: activeObject.top,
                scaleX: 1, // 重置缩放，因为我们已经调整了实际大小
                scaleY: 1
            });

            // 移除旧气泡
            this.canvas.remove(activeObject);

            // 添加新气泡
            this.canvas.add(newBubbleGroup);
            this.canvas.setActiveObject(newBubbleGroup);

            // 更新气泡列表
            const bubbleIndex = this.bubbles.findIndex(b => b === activeObject);
            if (bubbleIndex !== -1) {
                this.bubbles[bubbleIndex] = newBubbleGroup;
            }

            this.canvas.renderAll();
            this.updateStatus('气泡大小已自动调整');

        } catch (error) {
            console.error('自动调整气泡大小失败:', error);
            this.updateStatus('自动调整失败，请重试');
        }
    }

    /**
     * 获取画布实例
     */
    getCanvas() {
        return this.canvas;
    }
    
    /**
     * 获取所有气泡
     */
    getBubbles() {
        return this.bubbles;
    }

    /**
     * 裁切图片四边的指定像素
     */
    cropImage(img, cropPixels) {
        try {
            // 获取原始尺寸
            const originalWidth = img.width;
            const originalHeight = img.height;

            // 计算裁切后的尺寸
            const croppedWidth = originalWidth - (cropPixels * 2);
            const croppedHeight = originalHeight - (cropPixels * 2);

            // 检查裁切后尺寸是否有效
            if (croppedWidth <= 0 || croppedHeight <= 0) {
                console.warn(`裁切像素过大，图片尺寸不足。原始尺寸: ${originalWidth}x${originalHeight}, 裁切像素: ${cropPixels}`);
                this.updateStatus(`裁切像素过大，图片尺寸不足`);
                return img; // 返回原图
            }

            // 创建临时canvas进行裁切
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');

            // 设置临时canvas尺寸为裁切后的尺寸
            tempCanvas.width = croppedWidth;
            tempCanvas.height = croppedHeight;

            // 获取原始图片的canvas元素
            const imgElement = img.getElement();

            // 在临时canvas上绘制裁切后的图片
            // 从原图的(cropPixels, cropPixels)位置开始，绘制croppedWidth x croppedHeight的区域
            tempCtx.drawImage(
                imgElement,
                cropPixels, cropPixels, croppedWidth, croppedHeight, // 源图片的裁切区域
                0, 0, croppedWidth, croppedHeight // 目标canvas的绘制区域
            );

            // 将临时canvas转换为fabric.Image对象
            const croppedImg = new fabric.Image(tempCanvas, {
                left: 0,
                top: 0,
                originX: 'left',
                originY: 'top'
            });

            console.log(`图片裁切完成: ${originalWidth}x${originalHeight} -> ${croppedWidth}x${croppedHeight} (裁切${cropPixels}px)`);
            this.updateStatus(`图片已裁切: 原始${originalWidth}x${originalHeight} -> 裁切后${croppedWidth}x${croppedHeight}`);

            return croppedImg;

        } catch (error) {
            console.error('图片裁切失败:', error);
            this.updateStatus('图片裁切失败，使用原图');
            return img; // 返回原图
        }
    }

    /**
     * 设置拖拽导入功能
     */
    setupDragAndDrop() {
        const canvasContainer = document.getElementById('canvas-container');
        const emptyState = document.getElementById('empty-state');

        // 检查元素是否存在
        if (!canvasContainer) {
            console.error('canvas-container元素未找到，拖拽功能初始化失败');
            return;
        }

        // 防止默认的拖拽行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            canvasContainer.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });

        // 高亮拖拽区域
        ['dragenter', 'dragover'].forEach(eventName => {
            canvasContainer.addEventListener(eventName, () => {
                canvasContainer.classList.add('drag-highlight');
                if (emptyState && !emptyState.classList.contains('hidden')) {
                    emptyState.classList.add('drag-active');
                }
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            canvasContainer.addEventListener(eventName, () => {
                canvasContainer.classList.remove('drag-highlight');
                if (emptyState) {
                    emptyState.classList.remove('drag-active');
                }
            }, false);
        });

        // 处理文件拖拽
        canvasContainer.addEventListener('drop', (e) => {
            this.handleDrop(e);
        }, false);
    }

    /**
     * 防止默认拖拽行为
     */
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    /**
     * 处理文件拖拽
     */
    handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            const file = files[0];

            // 检查是否为图片文件
            if (!file.type.startsWith('image/')) {
                this.updateStatus('请拖入图片文件（JPG、PNG、GIF等）');
                return;
            }

            // 检查文件大小（限制为50MB）
            const maxSize = 50 * 1024 * 1024; // 50MB
            if (file.size > maxSize) {
                this.updateStatus('图片文件过大，请选择小于50MB的图片');
                return;
            }

            this.updateStatus('正在加载图片...');

            // 读取文件并设置为背景
            const reader = new FileReader();
            reader.onload = (event) => {
                const imageUrl = event.target.result;
                // 获取当前的裁切设置
                const cropPixels = window.app ? window.app.getCurrentSettings().cropPixels : 0;
                this.setBackgroundImage(imageUrl, () => {
                    this.updateStatus(`图片已导入: ${file.name}`);
                }, cropPixels);
            };

            reader.onerror = () => {
                this.updateStatus('图片读取失败，请重试');
            };

            reader.readAsDataURL(file);
        }
    }
}
