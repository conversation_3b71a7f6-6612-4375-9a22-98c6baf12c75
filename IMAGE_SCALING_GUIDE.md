# 图片缩放和居中功能使用说明

## 功能概述

该功能为漫画风格对话气泡编辑器添加了智能图片缩放和居中显示功能，提供更好的用户体验。

## 核心特性

### 1. 智能缩放
- **自动缩放**: 图片自动按编辑窗口比例缩放
- **只缩小不放大**: 保持图片原始质量，防止失真
- **保持宽高比**: 确保图片不会变形

### 2. 居中显示
- **自动居中**: 图片在编辑窗口中自动居中
- **响应式调整**: 窗口大小变化时自动重新计算位置

### 3. 配置选项
- **可配置参数**: 支持自定义缩放行为
- **降级策略**: 功能异常时自动回退到原始模式

## 使用方法

### 基本使用
1. 导入图片时，系统会自动检测图片尺寸
2. 如果图片大于编辑窗口，自动缩放到适合大小
3. 小图片保持原始尺寸，在窗口中居中显示
4. 支持拖拽导入和文件选择导入

### 配置参数
可以通过配置管理器调整以下参数：

```javascript
imageDisplayConfig: {
    autoScale: true,          // 是否自动缩放
    preventUpscaling: true,   // 是否禁止放大
    centerImage: true,        // 是否居中显示
    maxScale: 1.0,           // 最大缩放比例
    minScale: 0.1,           // 最小缩放比例
    padding: 20,             // 边距
    maintainAspectRatio: true // 是否保持宽高比
}
```

## 技术实现

### 1. 核心类

#### ViewportManager
- 负责视口尺寸管理
- 监听窗口大小变化
- 提供尺寸计算服务

#### ImageScaler
- 负责图片缩放计算
- 提供居中定位算法
- 支持配置自定义

#### CanvasManager (扩展)
- 集成缩放和居中功能
- 提供降级策略
- 性能优化处理

### 2. 缩放算法

```javascript
// 计算适合的缩放比例
const scaleX = containerWidth / imageWidth;
const scaleY = containerHeight / imageHeight;
const scale = Math.min(scaleX, scaleY, maxScale);
```

### 3. 居中算法

```javascript
// 计算居中位置
const centerX = (containerWidth - scaledWidth) / 2;
const centerY = (containerHeight - scaledHeight) / 2;
```

## 状态信息

应用会在状态栏显示图片信息：
- **原始尺寸**: 图片的原始分辨率
- **显示尺寸**: 当前显示的尺寸和缩放比例
- **位置信息**: 居中状态

示例：`原始: 1920x1080 | 显示: 960x540 (50%) | 居中`

## 性能优化

### 1. 防抖处理
- 窗口大小变化使用150ms防抖
- 避免频繁的重新计算

### 2. requestAnimationFrame
- 使用浏览器优化的动画帧
- 提供流畅的缩放体验

### 3. 错误处理
- 完善的异常捕获机制
- 自动降级策略

## 错误处理和降级

### 自动降级场景
1. 视口管理器初始化失败
2. 图片缩放器创建失败
3. 缩放计算出现异常

### 降级策略
- 回退到原始图片显示方式
- 保持基本的图片导入功能
- 显示相应的错误提示

## 兼容性

### 浏览器支持
- 现代浏览器（Chrome, Firefox, Safari, Edge）
- 支持ResizeObserver API
- 降级支持window.resize事件

### Electron环境
- 完全支持Electron应用
- 利用原生窗口管理功能

## 测试场景

### 已测试的图片尺寸
1. **小图片** (400x300): 保持原始尺寸，居中显示
2. **大图片** (2000x1500): 等比缩放到适合尺寸
3. **超宽图片** (3000x500): 按宽度比例缩放
4. **超高图片** (500x2000): 按高度比例缩放

### 窗口调整测试
- 拖拽调整窗口大小
- 最大化/最小化窗口
- 不同分辨率显示器

## 故障排除

### 常见问题

1. **图片不能正确缩放**
   - 检查浏览器控制台是否有错误
   - 确认图片格式是否支持
   - 尝试刷新页面重新加载

2. **窗口调整后图片位置异常**
   - 检查ResizeObserver支持情况
   - 确认防抖设置是否合理

3. **性能问题**
   - 检查图片文件大小
   - 确认是否启用了硬件加速

### 调试信息
在浏览器控制台可以看到：
- 缩放计算过程
- 视口尺寸变化
- 错误信息和警告

## 更新日志

### v1.0.0
- 实现基本的图片缩放和居中功能
- 添加配置管理支持
- 实现降级策略
- 性能优化和错误处理