/**
 * 配置管理器
 * 负责保存和加载用户配置
 */
class ConfigManager {
    constructor() {
        this.configFile = 'user-config.json';
        this.defaultConfig = {
            fontSize: 16,
            bubbleColor: '#ffffff',
            borderStyle: 'solid',
            bubbleType: 'speech',
            tailPosition: 'bottom-left',
            fontFamily: '沐瑶软笔手写体',
            opacity: 0.9, // 默认透明度90%
            cropPixels: 0, // 默认不裁切
            lastUsedDirectory: null,
            windowSize: {
                width: 1200,
                height: 800
            },
            recentFiles: [],
            // 图片显示配置
            imageDisplayConfig: {
                autoScale: true,          // 自动缩放
                preventUpscaling: true,   // 禁止放大
                centerImage: true,        // 居中显示
                maxScale: 1.0,           // 最大缩放比例
                minScale: 0.1,           // 最小缩放比例
                padding: 20,             // 边距
                maintainAspectRatio: true // 保持宽高比
            }
        };
        this.currentConfig = { ...this.defaultConfig };
    }
    
    /**
     * 初始化配置管理器
     */
    async init() {
        try {
            await this.loadConfig();
            console.log('配置管理器初始化完成，当前配置:', this.currentConfig);
        } catch (error) {
            console.error('配置管理器初始化失败:', error);
            this.currentConfig = { ...this.defaultConfig };
        }
    }
    
    /**
     * 加载配置文件
     */
    async loadConfig() {
        try {
            if (window.electronAPI && window.electronAPI.loadConfig) {
                const config = await window.electronAPI.loadConfig(this.configFile);
                if (config) {
                    // 合并配置，确保新增的配置项有默认值
                    this.currentConfig = { ...this.defaultConfig, ...config };
                    console.log('配置文件加载成功');
                } else {
                    console.log('配置文件不存在，使用默认配置');
                    this.currentConfig = { ...this.defaultConfig };
                }
            } else {
                // 如果electronAPI不可用，尝试从localStorage加载
                const savedConfig = localStorage.getItem('bubble-editor-config');
                if (savedConfig) {
                    const config = JSON.parse(savedConfig);
                    this.currentConfig = { ...this.defaultConfig, ...config };
                    console.log('从localStorage加载配置');
                } else {
                    console.log('使用默认配置');
                    this.currentConfig = { ...this.defaultConfig };
                }
            }
        } catch (error) {
            console.error('加载配置失败:', error);
            this.currentConfig = { ...this.defaultConfig };
        }
    }
    
    /**
     * 保存配置文件
     */
    async saveConfig() {
        try {
            if (window.electronAPI && window.electronAPI.saveConfig) {
                await window.electronAPI.saveConfig(this.configFile, this.currentConfig);
                console.log('配置文件保存成功');
            } else {
                // 如果electronAPI不可用，保存到localStorage
                localStorage.setItem('bubble-editor-config', JSON.stringify(this.currentConfig));
                console.log('配置保存到localStorage');
            }
        } catch (error) {
            console.error('保存配置失败:', error);
        }
    }
    
    /**
     * 获取配置项
     */
    get(key) {
        return this.currentConfig[key];
    }
    
    /**
     * 设置配置项
     */
    set(key, value) {
        this.currentConfig[key] = value;
        // 自动保存配置
        this.saveConfig();
    }
    
    /**
     * 批量设置配置项
     */
    setMultiple(config) {
        Object.assign(this.currentConfig, config);
        this.saveConfig();
    }
    
    /**
     * 获取所有配置
     */
    getAll() {
        return { ...this.currentConfig };
    }
    
    /**
     * 重置为默认配置
     */
    reset() {
        this.currentConfig = { ...this.defaultConfig };
        this.saveConfig();
    }
    
    /**
     * 添加最近使用的文件
     */
    addRecentFile(filePath) {
        const recentFiles = this.currentConfig.recentFiles || [];
        
        // 移除已存在的相同路径
        const index = recentFiles.indexOf(filePath);
        if (index > -1) {
            recentFiles.splice(index, 1);
        }
        
        // 添加到开头
        recentFiles.unshift(filePath);
        
        // 限制最多10个
        if (recentFiles.length > 10) {
            recentFiles.splice(10);
        }
        
        this.currentConfig.recentFiles = recentFiles;
        this.saveConfig();
    }
    
    /**
     * 获取最近使用的文件
     */
    getRecentFiles() {
        return this.currentConfig.recentFiles || [];
    }
    
    /**
     * 更新窗口大小
     */
    updateWindowSize(width, height) {
        this.currentConfig.windowSize = { width, height };
        this.saveConfig();
    }
    
    /**
     * 获取图片显示配置
     */
    getImageDisplayConfig() {
        return this.currentConfig.imageDisplayConfig || this.defaultConfig.imageDisplayConfig;
    }
    
    /**
     * 设置图片显示配置
     */
    setImageDisplayConfig(config) {
        this.currentConfig.imageDisplayConfig = {
            ...this.getImageDisplayConfig(),
            ...config
        };
        this.saveConfig();
    }
    
    /**
     * 获取窗口大小
     */
    getWindowSize() {
        return this.currentConfig.windowSize;
    }
}

// 导出配置管理器
window.ConfigManager = ConfigManager;
