<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>椭圆形对话气泡演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .demo-section {
            margin: 40px 0;
        }
        
        .demo-section h2 {
            color: #555;
            border-bottom: 2px solid #74b9ff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .bubble-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: center;
            align-items: flex-start;
            margin: 20px 0;
        }
        
        /* 椭圆形气泡基础样式 */
        .ellipse-bubble {
            position: relative;
            background: white;
            border: 3px solid #2d3436;
            padding: 16px 24px;
            max-width: 280px;
            min-width: 120px;
            font-size: 16px;
            line-height: 1.4;
            box-shadow: 4px 4px 0 rgba(0,0,0,0.15);
            margin: 25px;
            text-align: center;
            
            /* 椭圆形状 */
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 80px;
        }
        
        /* 左下角椭圆气泡 */
        .ellipse-bottom-left::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 25%;
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-top: 18px solid white;
            border-bottom: none;
            transform: translateX(-50%);
        }
        .ellipse-bottom-left::before {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 25%;
            width: 0;
            height: 0;
            border: 14px solid transparent;
            border-top: 20px solid #2d3436;
            border-bottom: none;
            transform: translateX(-50%);
        }
        
        /* 右下角椭圆气泡 */
        .ellipse-bottom-right::after {
            content: '';
            position: absolute;
            bottom: -8px;
            right: 25%;
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-top: 18px solid white;
            border-bottom: none;
            transform: translateX(50%);
        }
        .ellipse-bottom-right::before {
            content: '';
            position: absolute;
            bottom: -12px;
            right: 25%;
            width: 0;
            height: 0;
            border: 14px solid transparent;
            border-top: 20px solid #2d3436;
            border-bottom: none;
            transform: translateX(50%);
        }
        
        /* 左侧椭圆气泡 */
        .ellipse-left::after {
            content: '';
            position: absolute;
            left: -8px;
            top: 50%;
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-right: 18px solid white;
            border-left: none;
            transform: translateY(-50%);
        }
        .ellipse-left::before {
            content: '';
            position: absolute;
            left: -12px;
            top: 50%;
            width: 0;
            height: 0;
            border: 14px solid transparent;
            border-right: 20px solid #2d3436;
            border-left: none;
            transform: translateY(-50%);
        }
        
        /* 右侧椭圆气泡 */
        .ellipse-right::after {
            content: '';
            position: absolute;
            right: -8px;
            top: 50%;
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-left: 18px solid white;
            border-right: none;
            transform: translateY(-50%);
        }
        .ellipse-right::before {
            content: '';
            position: absolute;
            right: -12px;
            top: 50%;
            width: 0;
            height: 0;
            border: 14px solid transparent;
            border-left: 20px solid #2d3436;
            border-right: none;
            transform: translateY(-50%);
        }
        
        /* 思考椭圆气泡 */
        .ellipse-thought {
            background: #e8f4fd;
            border-color: #74b9ff;
            border-radius: 60% 40% 40% 60%;
        }
        .ellipse-thought::after {
            content: '';
            position: absolute;
            bottom: -25px;
            left: 30%;
            width: 12px;
            height: 12px;
            background: #e8f4fd;
            border: 3px solid #74b9ff;
            border-radius: 50%;
        }
        .ellipse-thought::before {
            content: '';
            position: absolute;
            bottom: -40px;
            left: 25%;
            width: 8px;
            height: 8px;
            background: #e8f4fd;
            border: 3px solid #74b9ff;
            border-radius: 50%;
            box-shadow: -15px -8px 0 -2px #e8f4fd, -15px -8px 0 1px #74b9ff;
        }
        
        /* 喊叫椭圆气泡 */
        .ellipse-shout {
            background: #ffeaa7;
            border: 4px solid #e17055;
            border-radius: 45% 55% 55% 45%;
            transform: rotate(-2deg);
        }
        .ellipse-shout::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 20%;
            width: 0;
            height: 0;
            border: 15px solid transparent;
            border-top: 22px solid #ffeaa7;
            border-bottom: none;
            transform: rotate(15deg);
        }
        .ellipse-shout::before {
            content: '';
            position: absolute;
            bottom: -17px;
            left: 18%;
            width: 0;
            height: 0;
            border: 17px solid transparent;
            border-top: 25px solid #e17055;
            border-bottom: none;
            transform: rotate(15deg);
        }
        
        .character {
            text-align: center;
            margin: 20px;
        }
        
        .character-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #fd79a8, #fdcb6e);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin: 0 auto 15px;
            color: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .scene {
            display: flex;
            justify-content: space-around;
            align-items: flex-end;
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(to bottom, #74b9ff, #a29bfe);
            border-radius: 20px;
            min-height: 250px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }
        
        .comparison h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #2d3436;
        }
        
        .old-style {
            background: white;
            border: 3px solid #2d3436;
            border-radius: 15px;
            padding: 16px 20px;
            position: relative;
            max-width: 250px;
            margin: 20px auto;
        }
        
        .old-style::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 30px;
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-top: 16px solid white;
            border-bottom: none;
        }
        .old-style::before {
            content: '';
            position: absolute;
            bottom: -16px;
            left: 28px;
            width: 0;
            height: 0;
            border: 14px solid transparent;
            border-top: 18px solid #2d3436;
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🥚 椭圆形漫画对话气泡</h1>
        
        <div class="demo-section">
            <h2>🎯 椭圆形对话气泡</h2>
            <div class="bubble-container">
                <div class="ellipse-bubble ellipse-bottom-left">
                    晚上想去哪里吃饭？
                </div>
                
                <div class="ellipse-bubble ellipse-bottom-right">
                    我想吃火锅！
                </div>
                
                <div class="ellipse-bubble ellipse-left">
                    左侧对话
                </div>
                
                <div class="ellipse-bubble ellipse-right">
                    右侧对话
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>💭 特殊椭圆气泡</h2>
            <div class="bubble-container">
                <div class="ellipse-bubble ellipse-thought">
                    嗯...今天吃什么好呢？
                </div>
                
                <div class="ellipse-bubble ellipse-shout">
                    太好吃了！！！
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎭 椭圆气泡对话场景</h2>
            <div class="scene">
                <div class="character">
                    <div class="character-avatar">👩</div>
                    <div class="ellipse-bubble ellipse-bottom-right">
                        扶着我啊！
                    </div>
                </div>
                
                <div class="character">
                    <div class="character-avatar">👨</div>
                    <div class="ellipse-bubble ellipse-bottom-left">
                        好的，小心点！
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>📊 样式对比</h2>
            <div class="comparison">
                <div>
                    <h3>❌ 旧版方形气泡</h3>
                    <div class="old-style">
                        看起来很正式，像公文
                    </div>
                </div>
                <div>
                    <h3>✅ 新版椭圆气泡</h3>
                    <div class="ellipse-bubble ellipse-bottom-left">
                        看起来更自然，像真正的对话
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>✨ 椭圆气泡的优势</h2>
            <ul style="font-size: 16px; line-height: 1.8; color: #555; max-width: 800px; margin: 0 auto;">
                <li><strong>🎨 更自然的外观</strong> - 椭圆形更符合人类说话时的自然形状</li>
                <li><strong>💬 漫画风格</strong> - 经典的漫画对话气泡都是椭圆形的</li>
                <li><strong>👁️ 视觉舒适</strong> - 圆润的边缘比尖锐的方角更柔和</li>
                <li><strong>🎯 焦点突出</strong> - 椭圆形能更好地聚焦读者注意力到文字内容</li>
                <li><strong>🔄 灵活适应</strong> - 可以根据文字长度自动调整椭圆的长宽比</li>
            </ul>
        </div>
    </div>
</body>
</html>
