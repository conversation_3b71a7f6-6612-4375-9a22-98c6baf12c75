<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
</head>
<body>
    <h1>Electron API 测试</h1>
    <div id="status"></div>
    <button onclick="testAPI()">测试API</button>
    
    <script>
        function updateStatus(message) {
            document.getElementById('status').innerHTML += '<p>' + message + '</p>';
        }
        
        function testAPI() {
            updateStatus('开始测试...');
            
            // 测试electronAPI是否存在
            if (window.electronAPI) {
                updateStatus('✅ electronAPI 可用');
                updateStatus('平台: ' + window.electronAPI.platform);
                
                // 测试path工具
                if (window.electronAPI.path) {
                    updateStatus('✅ path 工具可用');
                    try {
                        const testPath = 'C:\\test\\image.png';
                        const basename = window.electronAPI.path.basename(testPath);
                        const extname = window.electronAPI.path.extname(testPath);
                        updateStatus('basename: ' + basename);
                        updateStatus('extname: ' + extname);
                    } catch (error) {
                        updateStatus('❌ path 工具错误: ' + error.message);
                    }
                } else {
                    updateStatus('❌ path 工具不可用');
                }
                
                // 测试文件对话框
                updateStatus('测试文件对话框...');
                window.electronAPI.showOpenDialog().then(result => {
                    if (result.canceled) {
                        updateStatus('用户取消了文件选择');
                    } else {
                        updateStatus('选择的文件: ' + JSON.stringify(result.filePaths));
                    }
                }).catch(error => {
                    updateStatus('❌ 文件对话框错误: ' + error.message);
                });
                
            } else {
                updateStatus('❌ electronAPI 不可用');
            }
        }
        
        // 页面加载时自动测试
        window.addEventListener('DOMContentLoaded', () => {
            updateStatus('页面已加载');
            testAPI();
        });
    </script>
</body>
</html>
