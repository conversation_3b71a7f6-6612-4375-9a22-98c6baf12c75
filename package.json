{"name": "comic-speech-bubble-editor", "version": "1.0.0", "description": "一个为图片添加漫画风格对话气泡的桌面应用", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "comic", "speech-bubble", "image-editor"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"fabric": "^5.3.0"}, "build": {"appId": "com.example.comic-speech-bubble-editor", "productName": "漫画对话气泡编辑器", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "src/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "src/assets/icons/icon.ico"}, "mac": {"target": "dmg", "icon": "src/assets/icons/icon.icns"}, "linux": {"target": "AppImage", "icon": "src/assets/icons/icon.png"}}}