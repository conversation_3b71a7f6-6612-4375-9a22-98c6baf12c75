<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整气泡样式演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .demo-section {
            margin: 40px 0;
        }
        
        .demo-section h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .bubble-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            justify-content: center;
            align-items: flex-start;
            margin: 20px 0;
        }
        
        /* 基础气泡样式 */
        .speech-bubble {
            position: relative;
            background: white;
            border: 3px solid #000;
            border-radius: 20px;
            padding: 16px 20px;
            max-width: 250px;
            font-size: 16px;
            line-height: 1.3;
            box-shadow: 4px 4px 0 rgba(0,0,0,0.2);
            margin: 20px;
        }
        
        /* 左下角气泡 */
        .bubble-bottom-left::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 30px;
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-top: 16px solid white;
            border-bottom: none;
        }
        .bubble-bottom-left::before {
            content: '';
            position: absolute;
            bottom: -16px;
            left: 28px;
            width: 0;
            height: 0;
            border: 14px solid transparent;
            border-top: 18px solid #000;
            border-bottom: none;
        }
        
        /* 右下角气泡 */
        .bubble-bottom-right::after {
            content: '';
            position: absolute;
            bottom: -12px;
            right: 30px;
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-top: 16px solid white;
            border-bottom: none;
        }
        .bubble-bottom-right::before {
            content: '';
            position: absolute;
            bottom: -16px;
            right: 28px;
            width: 0;
            height: 0;
            border: 14px solid transparent;
            border-top: 18px solid #000;
            border-bottom: none;
        }
        
        /* 思考气泡 */
        .bubble-thought {
            background: #f0f8ff;
            border-radius: 30px;
            border-color: #4a90e2;
        }
        .bubble-thought::after {
            content: '';
            position: absolute;
            bottom: -25px;
            left: 40px;
            width: 12px;
            height: 12px;
            background: #f0f8ff;
            border: 3px solid #4a90e2;
            border-radius: 50%;
        }
        .bubble-thought::before {
            content: '';
            position: absolute;
            bottom: -40px;
            left: 35px;
            width: 8px;
            height: 8px;
            background: #f0f8ff;
            border: 3px solid #4a90e2;
            border-radius: 50%;
            box-shadow: -15px -8px 0 -2px #f0f8ff, -15px -8px 0 1px #4a90e2;
        }
        
        /* 喊叫气泡 */
        .bubble-shout {
            background: #fff3cd;
            border: 3px solid #856404;
            position: relative;
            overflow: visible;
        }
        .bubble-shout::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 25px;
            width: 0;
            height: 0;
            border: 15px solid transparent;
            border-top: 20px solid #fff3cd;
            border-bottom: none;
            transform: rotate(-10deg);
        }
        .bubble-shout::before {
            content: '';
            position: absolute;
            bottom: -19px;
            left: 22px;
            width: 0;
            height: 0;
            border: 17px solid transparent;
            border-top: 22px solid #856404;
            border-bottom: none;
            transform: rotate(-10deg);
        }
        
        /* 左侧气泡 */
        .bubble-left::after {
            content: '';
            position: absolute;
            left: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-right: 16px solid white;
            border-left: none;
        }
        .bubble-left::before {
            content: '';
            position: absolute;
            left: -16px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border: 14px solid transparent;
            border-right: 18px solid #000;
            border-left: none;
        }
        
        /* 右侧气泡 */
        .bubble-right::after {
            content: '';
            position: absolute;
            right: -12px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-left: 16px solid white;
            border-right: none;
        }
        .bubble-right::before {
            content: '';
            position: absolute;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border: 14px solid transparent;
            border-left: 18px solid #000;
            border-right: none;
        }
        
        .character {
            text-align: center;
            margin: 10px;
        }
        
        .character-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin: 0 auto 10px;
            color: white;
        }
        
        .scene {
            display: flex;
            justify-content: space-around;
            align-items: flex-end;
            margin: 40px 0;
            padding: 20px;
            background: linear-gradient(to bottom, #87ceeb, #98fb98);
            border-radius: 15px;
            min-height: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 完整的漫画对话气泡样式</h1>
        
        <div class="demo-section">
            <h2>📢 基础对话气泡</h2>
            <div class="bubble-container">
                <div class="speech-bubble bubble-bottom-left">
                    晚上想去哪里吃饭？
                </div>
                
                <div class="speech-bubble bubble-bottom-right">
                    我想吃火锅！你觉得呢？
                </div>
                
                <div class="speech-bubble bubble-left">
                    左侧对话气泡
                </div>
                
                <div class="speech-bubble bubble-right">
                    右侧对话气泡
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>💭 特殊类型气泡</h2>
            <div class="bubble-container">
                <div class="speech-bubble bubble-thought">
                    嗯...今天吃什么好呢？
                </div>
                
                <div class="speech-bubble bubble-shout">
                    太好吃了！！！
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎭 对话场景演示</h2>
            <div class="scene">
                <div class="character">
                    <div class="character-avatar">👩</div>
                    <div class="speech-bubble bubble-bottom-right">
                        扶着我啊！
                    </div>
                </div>
                
                <div class="character">
                    <div class="character-avatar">👨</div>
                    <div class="speech-bubble bubble-bottom-left">
                        好的，小心点！
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>✨ 设计特点</h2>
            <ul style="font-size: 16px; line-height: 1.6; color: #555;">
                <li><strong>一体化设计</strong> - 气泡主体和尾巴完全融合，没有分离感</li>
                <li><strong>多种位置</strong> - 支持左下、右下、左侧、右侧等多种尾巴位置</li>
                <li><strong>视觉层次</strong> - 使用阴影和边框创造立体效果</li>
                <li><strong>类型丰富</strong> - 对话、思考、喊叫等不同情感表达</li>
                <li><strong>响应式</strong> - 自动适应文字内容长度</li>
            </ul>
        </div>
    </div>
</body>
</html>
