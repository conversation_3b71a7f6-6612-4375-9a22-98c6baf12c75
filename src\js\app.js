/**
 * 主应用控制器 - 协调各个模块的工作
 */
class App {
    constructor() {
        this.canvasManager = null;
        this.fileManager = null;
        this.exportManager = null;
        this.fontManager = null;
        this.configManager = null;
        this.currentSettings = {
            fontSize: 16,
            bubbleColor: '#ffffff',
            borderStyle: 'solid',
            bubbleType: 'speech',
            tailPosition: 'bottom-left',
            fontFamily: '沐瑶软笔手写体', // 添加字体设置
            opacity: 0.9, // 默认透明度90%
            cropPixels: 0 // 默认不裁切
        };

        this.init();
    }
    
    /**
     * 初始化应用
     */
    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeApp();
            });
        } else {
            this.initializeApp();
        }
    }
    
    /**
     * 初始化应用组件
     */
    async initializeApp() {
        console.log('初始化漫画对话气泡编辑器...');

        // 检查electronAPI是否可用
        if (!window.electronAPI) {
            console.error('electronAPI不可用，请检查preload脚本');
            // 不显示错误，因为通知系统还没初始化
            document.getElementById('status-text').textContent = '系统初始化失败：electronAPI不可用';
            return;
        }

        console.log('electronAPI可用，平台:', window.electronAPI.platform);

        try {
            // 等待 Fabric.js 完全加载
            await this.waitForFabricJS();

            // 初始化配置管理器
            this.configManager = new ConfigManager();
            await this.configManager.init();

            // 初始化字体管理器
            this.fontManager = new FontManager();
            await this.fontManager.init();

            // 从配置加载设置
            this.loadSettingsFromConfig();

            // 初始化各个管理器（确保 Fabric.js 已加载）
            this.canvasManager = new CanvasManager();
            this.fileManager = new FileManager();
            this.exportManager = new ExportManager();

            // 绑定UI事件
            this.bindUIEvents();

            // 初始化UI状态
            this.initializeUI();

            // 应用设置到UI
            this.applySettingsToUI();

            console.log('应用初始化完成');
        } catch (error) {
            console.error('应用初始化失败:', error);
            document.getElementById('status-text').textContent = '应用初始化失败: ' + error.message;
        }
    }

    /**
     * 等待 Fabric.js 完全加载
     */
    async waitForFabricJS() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 50; // 最多等待5秒

            const checkFabric = () => {
                attempts++;

                if (typeof fabric !== 'undefined' && fabric.Canvas) {
                    console.log('Fabric.js 已加载完成');
                    resolve();
                } else if (attempts >= maxAttempts) {
                    reject(new Error('Fabric.js 加载超时'));
                } else {
                    setTimeout(checkFabric, 100);
                }
            };

            checkFabric();
        });
    }

    /**
     * 从配置加载设置
     */
    loadSettingsFromConfig() {
        if (!this.configManager) return;

        this.currentSettings = {
            fontSize: this.configManager.get('fontSize'),
            bubbleColor: this.configManager.get('bubbleColor'),
            borderStyle: this.configManager.get('borderStyle'),
            bubbleType: this.configManager.get('bubbleType'),
            tailPosition: this.configManager.get('tailPosition'),
            fontFamily: this.configManager.get('fontFamily'),
            opacity: this.configManager.get('opacity'),
            cropPixels: this.configManager.get('cropPixels')
        };

        console.log('从配置加载设置:', this.currentSettings);
    }

    /**
     * 应用设置到UI
     */
    applySettingsToUI() {
        // 字体大小
        const fontSizeSlider = document.getElementById('font-size-slider');
        const fontSizeValue = document.getElementById('font-size-value');
        if (fontSizeSlider && fontSizeValue) {
            fontSizeSlider.value = this.currentSettings.fontSize;
            fontSizeValue.textContent = this.currentSettings.fontSize + 'px';
        }

        // 字体选择
        const fontSelector = document.getElementById('font-family');
        if (fontSelector) {
            fontSelector.value = this.currentSettings.fontFamily;
        }

        // 透明度
        const opacitySlider = document.getElementById('bubble-opacity');
        const opacityValue = document.getElementById('opacity-value');
        if (opacitySlider && opacityValue) {
            opacitySlider.value = this.currentSettings.opacity;
            opacityValue.textContent = Math.round(this.currentSettings.opacity * 100) + '%';
        }

        // 裁切像素
        const cropSlider = document.getElementById('crop-pixels');
        const cropValue = document.getElementById('crop-value');
        if (cropSlider && cropValue) {
            cropSlider.value = this.currentSettings.cropPixels;
            cropValue.textContent = this.currentSettings.cropPixels + 'px';
        }

        // 气泡类型
        const bubbleType = document.getElementById('bubble-type');
        if (bubbleType) {
            bubbleType.value = this.currentSettings.bubbleType;
        }

        // 尾巴位置
        const tailPosition = document.getElementById('tail-position');
        if (tailPosition) {
            tailPosition.value = this.currentSettings.tailPosition;
        }

        // 边框样式
        const borderStyle = document.getElementById('border-style');
        if (borderStyle) {
            borderStyle.value = this.currentSettings.borderStyle;
        }

        // 颜色按钮
        const colorBtns = document.querySelectorAll('.color-btn');
        colorBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.color === this.currentSettings.bubbleColor) {
                btn.classList.add('active');
            }
        });
    }

    /**
     * 保存设置到配置
     */
    saveSettingsToConfig() {
        if (!this.configManager) return;

        this.configManager.setMultiple(this.currentSettings);
        console.log('设置已保存到配置');
    }

    /**
     * 绑定UI事件
     */
    bindUIEvents() {
        // 导入图片按钮
        const importBtn = document.getElementById('import-btn');
        if (importBtn) {
            importBtn.addEventListener('click', () => {
                this.handleImportImage();
            });
        } else {
            console.error('import-btn元素未找到');
        }
        
        // 添加气泡按钮
        const addBubbleBtn = document.getElementById('add-bubble-btn');
        if (addBubbleBtn) {
            addBubbleBtn.addEventListener('click', () => {
                this.handleAddBubble();
            });
        }

        // 文字输入框回车事件
        const textInput = document.getElementById('text-input');
        if (textInput) {
            textInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleAddBubble();
                }
            });
        }

        // 导出图片按钮
        const exportBtn = document.getElementById('export-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.handleExportImage();
            });
        }
        
        // 删除选中按钮
        const deleteBtn = document.getElementById('delete-selected-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                this.canvasManager.deleteSelectedObject();
            });
        }

        // 清空所有气泡按钮
        const clearAllBtn = document.getElementById('clear-all-btn');
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', () => {
                if (confirm('确定要清空所有气泡吗？')) {
                    this.canvasManager.clearAllBubbles();
                    this.updateClearAllButton();
                }
            });
        }
        
        // 字体大小滑块
        const fontSizeSlider = document.getElementById('font-size-slider');
        const fontSizeValue = document.getElementById('font-size-value');

        if (fontSizeSlider && fontSizeValue) {
            fontSizeSlider.addEventListener('input', (e) => {
                const fontSize = parseInt(e.target.value);
                this.currentSettings.fontSize = fontSize;
                fontSizeValue.textContent = fontSize + 'px';
                this.saveSettingsToConfig();
            });
        }
        
        // 颜色选择按钮
        document.querySelectorAll('.color-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // 移除其他按钮的active类
                document.querySelectorAll('.color-btn').forEach(b => b.classList.remove('active'));
                // 添加当前按钮的active类
                e.target.classList.add('active');
                // 更新当前设置
                this.currentSettings.bubbleColor = e.target.dataset.color;
                this.saveSettingsToConfig();
            });
        });
        
        // 气泡类型选择
        const bubbleType = document.getElementById('bubble-type');
        if (bubbleType) {
            bubbleType.addEventListener('change', (e) => {
                this.currentSettings.bubbleType = e.target.value;
                this.saveSettingsToConfig();
            });
        }

        // 尾巴位置选择
        const tailPosition = document.getElementById('tail-position');
        if (tailPosition) {
            tailPosition.addEventListener('change', (e) => {
                this.currentSettings.tailPosition = e.target.value;
                this.saveSettingsToConfig();
            });
        }

        // 边框样式选择
        const borderStyle = document.getElementById('border-style');
        if (borderStyle) {
            borderStyle.addEventListener('change', (e) => {
                this.currentSettings.borderStyle = e.target.value;
                this.saveSettingsToConfig();
            });
        }

        // 字体选择
        const fontFamily = document.getElementById('font-family');
        if (fontFamily) {
            fontFamily.addEventListener('change', (e) => {
                this.currentSettings.fontFamily = e.target.value;
                this.saveSettingsToConfig();
            });
        }

        // 透明度滑块
        const opacitySlider = document.getElementById('bubble-opacity');
        const opacityValue = document.getElementById('opacity-value');

        if (opacitySlider && opacityValue) {
            opacitySlider.addEventListener('input', (e) => {
                const opacity = parseFloat(e.target.value);
                this.currentSettings.opacity = opacity;
                opacityValue.textContent = Math.round(opacity * 100) + '%';
                this.saveSettingsToConfig();
            });
        }

        // 裁切像素滑块
        const cropSlider = document.getElementById('crop-pixels');
        const cropValue = document.getElementById('crop-value');

        if (cropSlider && cropValue) {
            cropSlider.addEventListener('input', (e) => {
                const cropPixels = parseInt(e.target.value);
                this.currentSettings.cropPixels = cropPixels;
                cropValue.textContent = cropPixels + 'px';
                this.saveSettingsToConfig();
            });
        }

        // 窗口大小改变事件
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });
    }
    
    /**
     * 初始化UI状态
     */
    initializeUI() {
        // 设置初始字体大小显示
        document.getElementById('font-size-value').textContent = this.currentSettings.fontSize + 'px';
        
        // 禁用需要图片的按钮
        document.getElementById('export-btn').disabled = true;
        document.getElementById('delete-selected-btn').disabled = true;
        document.getElementById('clear-all-btn').disabled = true;
        
        // 显示空状态
        document.getElementById('empty-state').classList.remove('hidden');
    }
    
    /**
     * 处理导入图片
     */
    async handleImportImage() {
        try {
            // 显示加载状态
            this.canvasManager.updateStatus('正在加载图片...');
            const importBtn = document.getElementById('import-btn');
            const originalText = importBtn.innerHTML;
            importBtn.innerHTML = '<span class="btn-icon">⏳</span>加载中...';
            importBtn.disabled = true;

            const result = await this.fileManager.selectImageFile();

            if (result.success && result.imageUrl) {
                // 传递当前的裁切设置
                this.canvasManager.setBackgroundImage(result.imageUrl, (error) => {
                    // 恢复按钮状态
                    importBtn.innerHTML = originalText;
                    importBtn.disabled = false;

                    if (error) {
                        this.showError('图片导入失败: ' + error.message);
                    } else {
                        this.updateClearAllButton();
                        this.showSuccess('图片导入成功');
                    }
                }, this.currentSettings.cropPixels);
            } else {
                // 恢复按钮状态
                importBtn.innerHTML = originalText;
                importBtn.disabled = false;

                if (result.message && !result.message.includes('用户取消')) {
                    this.showError(result.message);
                }
            }
        } catch (error) {
            console.error('导入图片失败:', error);

            // 恢复按钮状态
            const importBtn = document.getElementById('import-btn');
            importBtn.innerHTML = '<span class="btn-icon">📁</span>导入图片';
            importBtn.disabled = false;

            this.showError('导入图片失败: ' + error.message);
        }
    }
    
    /**
     * 处理添加气泡
     */
    handleAddBubble() {
        const textInput = document.getElementById('text-input');
        const text = textInput.value.trim();
        
        if (!text) {
            this.canvasManager.updateStatus('请输入对话内容');
            textInput.focus();
            return;
        }
        
        if (!this.canvasManager.backgroundImage) {
            this.canvasManager.updateStatus('请先导入背景图片');
            return;
        }
        
        // 使用当前设置创建气泡
        const bubbleOptions = {
            fontSize: this.currentSettings.fontSize,
            fill: this.currentSettings.bubbleColor,
            borderStyle: this.currentSettings.borderStyle,
            bubbleType: this.currentSettings.bubbleType,
            tailPosition: this.currentSettings.tailPosition,
            fontFamily: this.currentSettings.fontFamily, // 添加字体设置
            opacity: this.currentSettings.opacity // 添加透明度设置
        };
        
        const bubble = this.canvasManager.addSpeechBubble(text, bubbleOptions);
        
        if (bubble) {
            // 清空输入框
            textInput.value = '';
            textInput.focus();
            
            // 更新清空按钮状态
            this.updateClearAllButton();
        }
    }
    
    /**
     * 处理导出图片
     */
    async handleExportImage() {
        if (!this.canvasManager.backgroundImage) {
            this.canvasManager.updateStatus('没有可导出的内容');
            return;
        }

        try {
            this.canvasManager.updateStatus('正在导出高清图片...');

            // 计算导出倍率以实现高清导出
            const exportMultiplier = this.canvasManager.calculateExportMultiplier();

            console.log(`使用导出倍率: ${exportMultiplier.toFixed(3)}`);

            // 传递倍率参数给导出管理器
            const exportOptions = {
                multiplier: exportMultiplier,
                quality: 1.0 // 使用最高质量
            };

            const result = await this.exportManager.exportCanvas(
                this.canvasManager.getCanvas(),
                exportOptions
            );

            if (result.success) {
                const sizeInfo = exportMultiplier > 1 ? ` (高清 ${exportMultiplier.toFixed(1)}x)` : '';
                this.canvasManager.updateStatus(`图片已保存: ${result.fileName}${sizeInfo}`);
                this.showSuccess(`高清图片导出成功${sizeInfo}`);
            } else {
                this.canvasManager.updateStatus('导出失败: ' + result.error);
                this.showError('导出失败: ' + result.error);
            }
        } catch (error) {
            console.error('导出图片失败:', error);
            this.canvasManager.updateStatus('导出图片失败: ' + error.message);
            this.showError('导出图片失败: ' + error.message);
        }
    }
    
    /**
     * 处理窗口大小改变
     */
    handleWindowResize() {
        // 可以在这里添加响应式处理逻辑
        // 例如重新调整画布大小以适应新的窗口尺寸
        if (this.canvasManager && this.canvasManager.backgroundImage) {
            // 延迟执行以避免频繁调用
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(() => {
                // 这里可以添加重新调整画布的逻辑
                console.log('窗口大小已改变');
            }, 300);
        }
    }
    
    /**
     * 更新清空按钮状态
     */
    updateClearAllButton() {
        const clearAllBtn = document.getElementById('clear-all-btn');
        const hasBubbles = this.canvasManager.getBubbles().length > 0;
        clearAllBtn.disabled = !hasBubbles;
    }
    
    /**
     * 获取当前设置
     */
    getCurrentSettings() {
        return { ...this.currentSettings };
    }
    
    /**
     * 更新设置
     */
    updateSettings(newSettings) {
        this.currentSettings = { ...this.currentSettings, ...newSettings };
    }
    
    /**
     * 显示错误消息
     */
    showError(message) {
        console.error('应用错误:', message);
        this.canvasManager.updateStatus('错误: ' + message);

        // 显示错误通知
        this.showNotification(message, 'error');
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        console.log('操作成功:', message);
        this.canvasManager.updateStatus(message);

        // 显示成功通知
        this.showNotification(message, 'success');
    }

    /**
     * 显示通知消息
     */
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${this.getNotificationIcon(type)}</span>
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // 添加样式
        if (!document.getElementById('notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'notification-styles';
            styles.textContent = `
                .notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    max-width: 400px;
                    padding: 12px;
                    border-radius: 6px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 1000;
                    animation: slideIn 0.3s ease;
                }
                .notification-success { background: #d4edda; border-left: 4px solid #28a745; color: #155724; }
                .notification-error { background: #f8d7da; border-left: 4px solid #dc3545; color: #721c24; }
                .notification-info { background: #d1ecf1; border-left: 4px solid #17a2b8; color: #0c5460; }
                .notification-content {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }
                .notification-icon { font-size: 16px; }
                .notification-message { flex: 1; font-size: 14px; }
                .notification-close {
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    opacity: 0.7;
                }
                .notification-close:hover { opacity: 1; }
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(styles);
        }

        // 添加到页面
        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    /**
     * 获取通知图标
     */
    getNotificationIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            info: 'ℹ️',
            warning: '⚠️'
        };
        return icons[type] || icons.info;
    }
}

// 全局应用实例
let app;

// 当页面加载完成时启动应用
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        app = new App();
    });
} else {
    app = new App();
}

// 导出到全局作用域以便调试
window.app = app;
