/**
 * 导出管理器 - 处理画布内容的导出功能
 */
class ExportManager {
    constructor() {
        this.supportedFormats = ['png', 'jpg', 'jpeg'];
        this.defaultQuality = 0.9;
        
        console.log('导出管理器初始化完成');
    }
    
    /**
     * 导出画布内容
     */
    async exportCanvas(canvas, options = {}) {
        try {
            if (!canvas) {
                throw new Error('画布对象不存在');
            }
            
            // 显示保存对话框
            const saveResult = await window.electronAPI.showSaveDialog();
            
            if (saveResult.canceled || !saveResult.filePath) {
                return { success: false, message: '用户取消了保存操作' };
            }
            
            const filePath = saveResult.filePath;
            const fileName = window.electronAPI.path.basename(filePath);
            const extension = window.electronAPI.path.extname(filePath).toLowerCase().slice(1) || 'png';
            
            console.log('导出文件路径:', filePath);
            console.log('文件格式:', extension);
            
            // 验证文件格式
            if (!this.supportedFormats.includes(extension)) {
                throw new Error(`不支持的导出格式: ${extension}`);
            }
            
            // 获取画布数据
            const canvasData = await this.getCanvasData(canvas, extension, options);
            
            // 保存文件
            await window.electronAPI.writeFile(filePath, canvasData);
            
            return {
                success: true,
                filePath: filePath,
                fileName: fileName,
                format: extension
            };
            
        } catch (error) {
            console.error('导出失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * 获取画布数据
     */
    async getCanvasData(canvas, format = 'png', options = {}) {
        return new Promise((resolve, reject) => {
            try {
                // 设置导出选项
                const exportOptions = {
                    format: format === 'jpg' ? 'jpeg' : format,
                    quality: options.quality || this.defaultQuality,
                    multiplier: options.multiplier || 1,
                    left: options.left,
                    top: options.top,
                    width: options.width,
                    height: options.height
                };
                
                // 获取画布数据URL
                const dataURL = canvas.toDataURL(exportOptions);
                
                if (!dataURL || dataURL === 'data:,') {
                    reject(new Error('无法获取画布数据'));
                    return;
                }
                
                resolve(dataURL);
                
            } catch (error) {
                reject(new Error('获取画布数据失败: ' + error.message));
            }
        });
    }
    
    /**
     * 导出高质量图片
     */
    async exportHighQuality(canvas, options = {}) {
        const highQualityOptions = {
            quality: 1.0,
            multiplier: options.scale || 2,
            ...options
        };
        
        return this.exportCanvas(canvas, highQualityOptions);
    }
    
    /**
     * 导出指定区域
     */
    async exportRegion(canvas, region, options = {}) {
        const regionOptions = {
            left: region.left || 0,
            top: region.top || 0,
            width: region.width || canvas.width,
            height: region.height || canvas.height,
            ...options
        };
        
        return this.exportCanvas(canvas, regionOptions);
    }
    
    /**
     * 批量导出不同格式
     */
    async exportMultipleFormats(canvas, formats = ['png', 'jpg'], options = {}) {
        const results = [];
        
        for (const format of formats) {
            try {
                const result = await this.exportCanvas(canvas, { ...options, format });
                results.push({
                    format: format,
                    success: result.success,
                    filePath: result.filePath,
                    error: result.error
                });
            } catch (error) {
                results.push({
                    format: format,
                    success: false,
                    error: error.message
                });
            }
        }
        
        return results;
    }
    
    /**
     * 预览导出效果
     */
    previewExport(canvas, format = 'png', options = {}) {
        try {
            const dataURL = canvas.toDataURL({
                format: format === 'jpg' ? 'jpeg' : format,
                quality: options.quality || this.defaultQuality,
                multiplier: options.multiplier || 1
            });
            
            // 创建预览窗口
            const previewWindow = window.open('', '_blank', 'width=800,height=600');
            previewWindow.document.write(`
                <html>
                    <head>
                        <title>导出预览</title>
                        <style>
                            body {
                                margin: 0;
                                padding: 20px;
                                background: #f0f0f0;
                                font-family: Arial, sans-serif;
                            }
                            .preview-container {
                                text-align: center;
                                background: white;
                                padding: 20px;
                                border-radius: 8px;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                            }
                            .preview-image {
                                max-width: 100%;
                                max-height: 70vh;
                                border: 1px solid #ddd;
                                border-radius: 4px;
                            }
                            .preview-info {
                                margin-top: 15px;
                                color: #666;
                                font-size: 14px;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="preview-container">
                            <h2>导出预览</h2>
                            <img src="${dataURL}" alt="导出预览" class="preview-image">
                            <div class="preview-info">
                                格式: ${format.toUpperCase()} | 
                                质量: ${Math.round((options.quality || this.defaultQuality) * 100)}% |
                                尺寸: ${canvas.width} × ${canvas.height}
                            </div>
                        </div>
                    </body>
                </html>
            `);
            
            return { success: true, previewWindow };
            
        } catch (error) {
            console.error('预览失败:', error);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 获取导出信息
     */
    getExportInfo(canvas, format = 'png', options = {}) {
        try {
            const dataURL = canvas.toDataURL({
                format: format === 'jpg' ? 'jpeg' : format,
                quality: options.quality || this.defaultQuality,
                multiplier: options.multiplier || 1
            });
            
            // 计算文件大小（近似值）
            const base64Length = dataURL.split(',')[1].length;
            const sizeInBytes = Math.round(base64Length * 0.75);
            
            return {
                format: format,
                width: canvas.width,
                height: canvas.height,
                estimatedSize: this.formatFileSize(sizeInBytes),
                quality: options.quality || this.defaultQuality,
                multiplier: options.multiplier || 1
            };
            
        } catch (error) {
            console.error('获取导出信息失败:', error);
            return null;
        }
    }
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 设置默认质量
     */
    setDefaultQuality(quality) {
        if (quality >= 0 && quality <= 1) {
            this.defaultQuality = quality;
        }
    }
    
    /**
     * 获取支持的导出格式
     */
    getSupportedFormats() {
        return [...this.supportedFormats];
    }
    
    /**
     * 验证导出选项
     */
    validateExportOptions(options) {
        const errors = [];
        
        if (options.quality !== undefined) {
            if (typeof options.quality !== 'number' || options.quality < 0 || options.quality > 1) {
                errors.push('质量参数必须是0-1之间的数字');
            }
        }
        
        if (options.multiplier !== undefined) {
            if (typeof options.multiplier !== 'number' || options.multiplier <= 0) {
                errors.push('缩放倍数必须是大于0的数字');
            }
        }
        
        if (options.format !== undefined) {
            if (!this.supportedFormats.includes(options.format.toLowerCase())) {
                errors.push(`不支持的格式: ${options.format}`);
            }
        }
        
        return {
            valid: errors.length === 0,
            errors: errors
        };
    }
}
