/* 字体加载 */
@font-face {
    font-family: '沐瑶软笔手写体';
    src: url('../fonts/沐瑶软笔手写体.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: '包图小白体';
    src: url('../fonts/包图小白体.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: '思源柔黑';
    src: url('../fonts/思源柔黑.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: '清松手寫體1';
    src: url('../fonts/清松手寫體1.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background: #f5f5f5;
    color: #333;
    overflow: hidden;
}

/* 应用容器 */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 100;
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-section.flex-grow {
    flex: 1;
    justify-content: center;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #1e7e34;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background: #138496;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover:not(:disabled) {
    background: #e0a800;
}

.btn-icon {
    font-size: 16px;
}

/* 输入组样式 */
.input-group {
    display: flex;
    align-items: center;
    gap: 8px;
    max-width: 500px;
    width: 100%;
}

.text-input {
    flex: 1;
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.text-input:focus {
    outline: none;
    border-color: #007bff;
}

/* 主内容区域 */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 画布容器 */
.canvas-container {
    flex: 1;
    position: relative;
    background: #f8f9fa;
    overflow: auto; /* 默认允许滚动 */
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* 拖拽高亮效果 */
.canvas-container.drag-highlight {
    background: #e3f2fd;
    border: 2px dashed #2196f3;
    box-shadow: inset 0 0 20px rgba(33, 150, 243, 0.1);
}

.canvas-wrapper {
    /* 移除绝对定位，使用弹性布局 */
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 100%;
    min-height: 100%;
}

#main-canvas {
    max-width: calc(100vw - 320px); /* 考虑侧边栏宽度 */
    max-height: calc(100vh - 200px); /* 考虑工具栏和状态栏高度 */
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
    background: white;
    display: block;
    margin: 0 auto;
}

/* 空状态样式 */
.empty-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(248, 249, 250, 0.9);
    z-index: 10;
}

.empty-state.hidden {
    display: none;
}

/* 拖拽激活状态 */
.empty-state.drag-active {
    background: rgba(227, 242, 253, 0.95);
    border: 2px dashed #2196f3;
}

.empty-state.drag-active .empty-state-content {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.empty-state.drag-active .empty-state-icon {
    color: #2196f3;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.empty-state-content {
    text-align: center;
    padding: 40px;
}

.empty-state-icon {
    font-size: 64px;
    margin-bottom: 16px;
}

.empty-state-content h3 {
    margin-bottom: 8px;
    color: #495057;
}

.empty-state-content p {
    margin-bottom: 24px;
    color: #6c757d;
}

/* 侧边面板 */
.side-panel {
    width: 280px;
    background: white;
    border-left: 1px solid #e0e0e0;
    overflow-y: auto;
    flex-shrink: 0;
}

.panel-section {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.panel-section h4 {
    margin-bottom: 12px;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

/* 样式控制 */
.style-controls {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.control-group label {
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
}

.slider {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: none;
}

/* 颜色选项 */
.color-options {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.color-btn {
    width: 32px;
    height: 32px;
    border: 2px solid transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.color-btn:hover {
    transform: scale(1.1);
}

.color-btn.active {
    border-color: #007bff !important;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.select-input {
    padding: 6px 8px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

/* 滑块容器 */
.slider-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 滑块样式 */
.slider {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: #e0e0e0;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider-value {
    font-size: 11px;
    color: #6c757d;
    min-width: 35px;
    text-align: right;
}

/* 裁切信息样式 */
.crop-info {
    margin-top: 5px;
}

.crop-info small {
    color: #6c757d;
    font-size: 10px;
    line-height: 1.3;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-buttons .btn {
    justify-content: flex-start;
    font-size: 12px;
    padding: 8px 12px;
}

/* 帮助内容 */
.help-content ul {
    list-style: none;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.5;
}

.help-content li {
    margin-bottom: 4px;
    padding-left: 12px;
    position: relative;
}

.help-content li:before {
    content: "•";
    position: absolute;
    left: 0;
    color: #007bff;
}

/* 状态栏 */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    font-size: 12px;
    color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .side-panel {
        width: 240px;
    }
    
    .toolbar-section.flex-grow .input-group {
        max-width: 300px;
    }
    
    #main-canvas {
        max-width: calc(100vw - 280px); /* 调整侧边栏宽度 */
    }
}

@media (max-width: 768px) {
    .side-panel {
        display: none;
    }
    
    .toolbar {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .toolbar-section {
        flex: none;
    }
    
    .toolbar-section.flex-grow {
        flex: 1 1 100%;
        order: 3;
        margin-top: 8px;
    }
    
    #main-canvas {
        max-width: calc(100vw - 40px); /* 无侧边栏时的宽度 */
    }
    
    .canvas-container {
        padding: 10px; /* 小屏幕上减少内边距 */
    }
}
