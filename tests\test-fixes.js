/**
 * 测试修复效果的脚本
 * 这个脚本可以在浏览器控制台中运行，用于验证修复是否成功
 */

// 测试结果收集器
const testResults = {
    passed: 0,
    failed: 0,
    tests: []
};

// 测试工具函数
function assert(condition, message) {
    if (condition) {
        testResults.passed++;
        testResults.tests.push({ status: 'PASS', message });
        console.log(`✅ PASS: ${message}`);
    } else {
        testResults.failed++;
        testResults.tests.push({ status: 'FAIL', message });
        console.error(`❌ FAIL: ${message}`);
    }
}

function testGroup(name, testFn) {
    console.group(`🧪 ${name}`);
    try {
        testFn();
    } catch (error) {
        console.error(`测试组 "${name}" 执行失败:`, error);
    }
    console.groupEnd();
}

// 主要测试函数
function runAllTests() {
    console.clear();
    console.log('🚀 开始运行修复验证测试...\n');
    
    testGroup('基础环境检查', testBasicEnvironment);
    testGroup('CanvasManager 初始化测试', testCanvasManagerInitialization);
    testGroup('图片缩放功能测试', testImageScalingFeatures);
    testGroup('高清导出功能测试', testHighResolutionExport);
    
    // 输出测试结果
    console.log('\n📊 测试结果汇总:');
    console.log(`✅ 通过: ${testResults.passed}`);
    console.log(`❌ 失败: ${testResults.failed}`);
    console.log(`📈 成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    
    if (testResults.failed === 0) {
        console.log('🎉 所有测试通过！修复成功！');
    } else {
        console.log('⚠️ 部分测试失败，需要进一步检查');
    }
    
    return testResults;
}

// 基础环境检查
function testBasicEnvironment() {
    assert(typeof window !== 'undefined', 'Window 对象存在');
    assert(typeof document !== 'undefined', 'Document 对象存在');
    assert(typeof fabric !== 'undefined', 'Fabric.js 已加载');
    assert(typeof fabric.Canvas === 'function', 'Fabric.Canvas 构造函数可用');
    
    const canvasContainer = document.getElementById('canvas-container');
    assert(canvasContainer !== null, 'canvas-container 元素存在');
    
    const mainCanvas = document.getElementById('main-canvas');
    assert(mainCanvas !== null, 'main-canvas 元素存在');
    
    assert(typeof window.app !== 'undefined', 'App 实例存在');
}

// CanvasManager 初始化测试
function testCanvasManagerInitialization() {
    const app = window.app;
    
    assert(app !== null && app !== undefined, 'App 实例不为空');
    assert(app.canvasManager !== null && app.canvasManager !== undefined, 'CanvasManager 实例存在');
    
    const canvasManager = app.canvasManager;
    
    // 检查是否成功避免了降级模式
    assert(canvasManager.viewportManager !== null, 'ViewportManager 成功创建（未进入降级模式）');
    assert(canvasManager.imageScaler !== null, 'ImageScaler 成功创建（未进入降级模式）');
    assert(canvasManager.canvas !== null, 'Fabric Canvas 成功创建');
    
    // 检查 ViewportManager 是否正确初始化
    if (canvasManager.viewportManager) {
        assert(canvasManager.viewportManager.canvasContainer !== null, 'ViewportManager 找到了 canvas-container');
    }
    
    // 检查 ImageScaler 是否有正确的配置
    if (canvasManager.imageScaler) {
        const config = canvasManager.imageScaler.getConfig();
        assert(typeof config === 'object', 'ImageScaler 配置对象存在');
        assert(config.autoScale === true, 'ImageScaler 启用了自动缩放');
    }
}

// 图片缩放功能测试
function testImageScalingFeatures() {
    const canvasManager = window.app?.canvasManager;
    
    if (!canvasManager) {
        console.warn('CanvasManager 不存在，跳过图片缩放测试');
        return;
    }
    
    // 测试缩放计算功能
    if (canvasManager.imageScaler) {
        const scale = canvasManager.imageScaler.calculateFitScale(1000, 800, 500, 400);
        assert(typeof scale === 'number' && scale > 0, 'ImageScaler 能够计算缩放比例');
        assert(scale <= 1.0, '缩放比例不超过1.0（防止放大）');
        
        const transform = canvasManager.imageScaler.calculateImageTransform(1000, 800, 500, 400);
        assert(typeof transform === 'object', 'ImageScaler 能够计算完整变换信息');
        assert(typeof transform.scale === 'number', '变换信息包含缩放比例');
        assert(typeof transform.position === 'object', '变换信息包含位置信息');
    }
    
    // 测试视口管理功能
    if (canvasManager.viewportManager) {
        const viewport = canvasManager.viewportManager.getViewportDimensions();
        assert(typeof viewport === 'object', 'ViewportManager 能够获取视口尺寸');
        assert(viewport.width > 0 && viewport.height > 0, '视口尺寸有效');
        
        const editableArea = canvasManager.viewportManager.getEditableAreaDimensions();
        assert(typeof editableArea === 'object', 'ViewportManager 能够计算可编辑区域');
        assert(editableArea.width > 0 && editableArea.height > 0, '可编辑区域尺寸有效');
    }
}

// 高清导出功能测试
function testHighResolutionExport() {
    const canvasManager = window.app?.canvasManager;
    
    if (!canvasManager) {
        console.warn('CanvasManager 不存在，跳过高清导出测试');
        return;
    }
    
    // 测试导出倍率计算
    assert(typeof canvasManager.calculateExportMultiplier === 'function', 'calculateExportMultiplier 方法存在');
    
    // 模拟有背景图片的情况
    if (canvasManager.backgroundImage && canvasManager.originalImageData) {
        const multiplier = canvasManager.calculateExportMultiplier();
        assert(typeof multiplier === 'number' && multiplier > 0, '能够计算导出倍率');
        assert(multiplier >= 0.1 && multiplier <= 10.0, '导出倍率在合理范围内');
    } else {
        // 没有背景图片时的默认行为
        const multiplier = canvasManager.calculateExportMultiplier();
        assert(multiplier === 1.0, '无背景图片时返回默认倍率1.0');
    }
    
    // 测试导出管理器
    const exportManager = window.app?.exportManager;
    if (exportManager) {
        assert(typeof exportManager.exportCanvas === 'function', 'ExportManager.exportCanvas 方法存在');
        assert(typeof exportManager.getCanvasData === 'function', 'ExportManager.getCanvasData 方法存在');
    }
}

// 辅助函数：模拟图片导入测试
function simulateImageImport() {
    console.log('🖼️ 模拟图片导入测试...');
    
    // 创建一个测试用的图片数据URL
    const canvas = document.createElement('canvas');
    canvas.width = 1200;
    canvas.height = 800;
    const ctx = canvas.getContext('2d');
    
    // 绘制一个简单的测试图案
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 1200, 800);
    ctx.fillStyle = '#007bff';
    ctx.fillRect(100, 100, 1000, 600);
    ctx.fillStyle = '#ffffff';
    ctx.font = '48px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('测试图片', 600, 400);
    
    const testImageUrl = canvas.toDataURL();
    
    // 使用 CanvasManager 设置背景图片
    const canvasManager = window.app?.canvasManager;
    if (canvasManager) {
        canvasManager.setBackgroundImage(testImageUrl, (error) => {
            if (error) {
                console.error('模拟图片导入失败:', error);
            } else {
                console.log('✅ 模拟图片导入成功');
                
                // 运行图片相关的测试
                setTimeout(() => {
                    testGroup('图片导入后的功能测试', () => {
                        assert(canvasManager.backgroundImage !== null, '背景图片已设置');
                        assert(canvasManager.originalImageData !== null, '原始图片数据已保存');
                        
                        if (canvasManager.currentImageTransform) {
                            assert(typeof canvasManager.currentImageTransform.scale === 'number', '图片变换信息已计算');
                        }
                        
                        const multiplier = canvasManager.calculateExportMultiplier();
                        assert(typeof multiplier === 'number' && multiplier > 0, '导出倍率计算正常');
                    });
                }, 500);
            }
        });
    }
}

// 导出测试函数供控制台使用
window.testFixes = {
    runAllTests,
    simulateImageImport,
    testResults
};

console.log('🔧 修复测试脚本已加载');
console.log('使用 testFixes.runAllTests() 运行所有测试');
console.log('使用 testFixes.simulateImageImport() 模拟图片导入测试');
