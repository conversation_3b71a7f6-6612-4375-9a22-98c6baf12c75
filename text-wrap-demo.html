<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能文字换行演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .demo-section {
            margin: 40px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }
        
        .demo-section h2 {
            color: #0984e3;
            margin-bottom: 20px;
            border-bottom: 2px solid #74b9ff;
            padding-bottom: 10px;
        }
        
        .bubble-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }
        
        .bubble-example {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .bubble-example h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        /* 旧版气泡样式 - 太长 */
        .old-bubble {
            position: relative;
            background: white;
            border: 3px solid #dc3545;
            padding: 12px 20px;
            margin: 15px auto;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 60px;
            max-width: 400px;
            box-shadow: 3px 3px 0 rgba(220,53,69,0.3);
        }
        
        .old-bubble::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 30%;
            width: 0;
            height: 0;
            border: 10px solid transparent;
            border-top: 15px solid white;
            border-bottom: none;
            transform: translateX(-50%);
        }
        
        .old-bubble::before {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 30%;
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-top: 17px solid #dc3545;
            border-bottom: none;
            transform: translateX(-50%);
        }
        
        /* 新版气泡样式 - 多行 */
        .new-bubble {
            position: relative;
            background: white;
            border: 3px solid #28a745;
            padding: 12px 20px;
            margin: 15px auto;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 60px;
            max-width: 200px;
            text-align: center;
            line-height: 1.3;
            box-shadow: 3px 3px 0 rgba(40,167,69,0.3);
        }
        
        .new-bubble::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 30%;
            width: 0;
            height: 0;
            border: 10px solid transparent;
            border-top: 15px solid white;
            border-bottom: none;
            transform: translateX(-50%);
        }
        
        .new-bubble::before {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 30%;
            width: 0;
            height: 0;
            border: 12px solid transparent;
            border-top: 17px solid #28a745;
            border-bottom: none;
            transform: translateX(-50%);
        }
        
        .example-text {
            font-size: 14px;
            color: #666;
            margin: 10px 0;
            padding: 8px;
            background: #e9ecef;
            border-radius: 5px;
        }
        
        .break-rules {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .break-rules h3 {
            color: #0984e3;
            margin-bottom: 15px;
        }
        
        .rule-list {
            list-style: none;
            padding: 0;
        }
        
        .rule-list li {
            margin: 8px 0;
            padding: 8px 12px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #74b9ff;
        }
        
        .priority-high { border-left-color: #e74c3c; }
        .priority-medium { border-left-color: #f39c12; }
        .priority-low { border-left-color: #3498db; }
        .priority-lowest { border-left-color: #95a5a6; }
        
        .test-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .test-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-card h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .highlight-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .highlight-box strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 智能文字换行功能</h1>
        
        <div class="demo-section">
            <h2>🆚 换行效果对比</h2>
            <div class="bubble-comparison">
                <div class="bubble-example">
                    <h3>❌ 旧版 - 气泡太长</h3>
                    <div class="old-bubble">
                        哇，天好晚了哎 好像回不去宿舍了
                    </div>
                    <div class="example-text">
                        问题：一行显示，气泡过长，不美观
                    </div>
                </div>
                
                <div class="bubble-example">
                    <h3>✅ 新版 - 智能换行</h3>
                    <div class="new-bubble">
                        哇，天好晚了哎<br>好像回不去宿舍了
                    </div>
                    <div class="example-text">
                        改进：按语气词"哎"自动换行，更美观
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎯 智能换行规则</h2>
            <div class="break-rules">
                <h3>换行优先级（从高到低）</h3>
                <ul class="rule-list">
                    <li class="priority-high">
                        <strong>1. 句号标点</strong> - 。！？.!? 
                        <span style="color: #666;">（优先级最高）</span>
                    </li>
                    <li class="priority-medium">
                        <strong>2. 逗号标点</strong> - ，、,;；
                        <span style="color: #666;">（次优先级）</span>
                    </li>
                    <li class="priority-low">
                        <strong>3. 空格</strong> - 英文单词间的空格
                        <span style="color: #666;">（中等优先级）</span>
                    </li>
                    <li class="priority-lowest">
                        <strong>4. 语气词</strong> - 哎、啊、呀、哦、嗯、唉
                        <span style="color: #666;">（较低优先级）</span>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>📋 测试示例</h2>
            <div class="test-examples">
                <div class="test-card">
                    <h4>句号换行</h4>
                    <div class="new-bubble">
                        今天天气真好。<br>我们去公园吧！
                    </div>
                </div>
                
                <div class="test-card">
                    <h4>逗号换行</h4>
                    <div class="new-bubble">
                        买点水果，<br>苹果和香蕉
                    </div>
                </div>
                
                <div class="test-card">
                    <h4>空格换行</h4>
                    <div class="new-bubble">
                        Hello world<br>Nice to meet you
                    </div>
                </div>
                
                <div class="test-card">
                    <h4>语气词换行</h4>
                    <div class="new-bubble">
                        哇，天好晚了哎<br>好像回不去宿舍了
                    </div>
                </div>
            </div>
        </div>
        
        <div class="highlight-box">
            <strong>💡 智能换行的优势：</strong><br>
            • 自动识别最佳断点，让气泡更美观<br>
            • 支持中英文混合文本<br>
            • 优先在语义合理的地方换行<br>
            • 避免气泡过长影响视觉效果<br>
            • 保持文字的可读性和自然性
        </div>
        
        <div class="demo-section">
            <h2>🎮 立即体验</h2>
            <p style="text-align: center; font-size: 18px; color: #555;">
                现在就打开应用，输入长文字测试智能换行功能！<br>
                试试这些例子：<br>
                <code style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px; margin: 0 5px;">
                    "哇，天好晚了哎 好像回不去宿舍了"
                </code><br>
                <code style="background: #f8f9fa; padding: 4px 8px; border-radius: 4px; margin: 0 5px;">
                    "今天天气真好。我们去公园散步吧！"
                </code>
            </p>
        </div>
    </div>
</body>
</html>
